/**
** =====================================================================================
**
**       文件名称: Threading.hpp
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI Pro】现代化C++17线程安全系统 - 高性能并发与同步机制
**
**       主要功能:
**       - 现代化C++17线程安全与并发编程框架
**       - 高性能无锁数据结构与原子操作
**       - 线程池与任务调度系统
**       - 读写锁与条件变量同步机制
**       - 异步编程与Future/Promise模式
**       - 线程本地存储与TLS管理
**       - 死锁检测与并发调试工具
**
**       技术特性:
**       - 采用现代C++17标准与STL并发库
**       - 无锁编程与内存序优化
**       - RAII锁管理与异常安全保证
**       - 高性能线程池与工作窃取算法
**       - 可扩展的任务调度系统
**       - 线程安全的事件系统
**       - 并发性能监控与统计
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化线程安全系统
**                             2. 实现高性能线程池技术
**                             3. 添加无锁数据结构
**                             4. 集成异步编程模式
**                             5. 实现并发调试工具
**
** =====================================================================================
*/

#pragma once

#include "Base.hpp"
#include <thread>
#include <mutex>
#include <shared_mutex>
#include <condition_variable>
#include <atomic>
#include <future>
#include <queue>
#include <vector>
#include <functional>
#include <chrono>
#include <memory>
#include <type_traits>
#include <utility>

namespace HHBUIPro {
    
    // 现代化RAII锁管理
    template<typename MutexType>
    class ScopedLockHHBUI {
    public:
        explicit ScopedLockHHBUI(MutexType& mutex) : lock_(mutex) {}
        
        // 禁用拷贝和移动
        ScopedLockHHBUI(const ScopedLockHHBUI&) = delete;
        ScopedLockHHBUI& operator=(const ScopedLockHHBUI&) = delete;
        ScopedLockHHBUI(ScopedLockHHBUI&&) = delete;
        ScopedLockHHBUI& operator=(ScopedLockHHBUI&&) = delete;
        
    private:
        std::unique_lock<MutexType> lock_;
    };
    
    template<typename MutexType>
    class ScopedSharedLockHHBUI {
    public:
        explicit ScopedSharedLockHHBUI(MutexType& mutex) : lock_(mutex) {}
        
        // 禁用拷贝和移动
        ScopedSharedLockHHBUI(const ScopedSharedLockHHBUI&) = delete;
        ScopedSharedLockHHBUI& operator=(const ScopedSharedLockHHBUI&) = delete;
        ScopedSharedLockHHBUI(ScopedSharedLockHHBUI&&) = delete;
        ScopedSharedLockHHBUI& operator=(ScopedSharedLockHHBUI&&) = delete;
        
    private:
        std::shared_lock<MutexType> lock_;
    };
    
    // 便利宏定义
    #define HHBUI_SCOPED_LOCK(mutex) \
        HHBUIPro::ScopedLockHHBUI<std::remove_reference_t<decltype(mutex)>> \
        HHBUI_CONCAT(lock_, __LINE__)(mutex)
    
    #define HHBUI_SCOPED_SHARED_LOCK(mutex) \
        HHBUIPro::ScopedSharedLockHHBUI<std::remove_reference_t<decltype(mutex)>> \
        HHBUI_CONCAT(shared_lock_, __LINE__)(mutex)
    
    #define HHBUI_CONCAT_IMPL(x, y) x##y
    #define HHBUI_CONCAT(x, y) HHBUI_CONCAT_IMPL(x, y)
    
    // 高性能无锁队列
    template<typename T>
    class LockFreeQueueHHBUI {
    private:
        struct Node {
            std::atomic<T*> data{nullptr};
            std::atomic<Node*> next{nullptr};
        };
        
    public:
        LockFreeQueueHHBUI() {
            Node* dummy = new Node;
            head_.store(dummy);
            tail_.store(dummy);
        }
        
        ~LockFreeQueueHHBUI() {
            while (Node* oldHead = head_.load()) {
                head_.store(oldHead->next);
                delete oldHead;
            }
        }
        
        // 禁用拷贝，允许移动
        LockFreeQueueHHBUI(const LockFreeQueueHHBUI&) = delete;
        LockFreeQueueHHBUI& operator=(const LockFreeQueueHHBUI&) = delete;
        LockFreeQueueHHBUI(LockFreeQueueHHBUI&&) = default;
        LockFreeQueueHHBUI& operator=(LockFreeQueueHHBUI&&) = default;
        
        void enqueue(T item) {
            Node* newNode = new Node;
            T* data = new T(std::move(item));
            newNode->data.store(data);
            
            Node* prevTail = tail_.exchange(newNode);
            prevTail->next.store(newNode);
        }
        
        bool dequeue(T& result) {
            Node* head = head_.load();
            Node* next = head->next.load();
            
            if (next == nullptr) {
                return false; // 队列为空
            }
            
            T* data = next->data.load();
            if (data == nullptr) {
                return false; // 数据尚未准备好
            }
            
            result = *data;
            delete data;
            
            head_.store(next);
            delete head;
            
            return true;
        }
        
        bool empty() const {
            Node* head = head_.load();
            Node* next = head->next.load();
            return next == nullptr;
        }
        
    private:
        std::atomic<Node*> head_;
        std::atomic<Node*> tail_;
    };
    
    // 现代化线程池
    class ThreadPoolHHBUI {
    public:
        explicit ThreadPoolHHBUI(std::size_t numThreads = std::thread::hardware_concurrency()) 
            : stop_(false) {
            
            workers_.reserve(numThreads);
            for (std::size_t i = 0; i < numThreads; ++i) {
                workers_.emplace_back([this] { workerLoop(); });
            }
        }
        
        ~ThreadPoolHHBUI() {
            shutdown();
        }
        
        // 禁用拷贝和移动
        ThreadPoolHHBUI(const ThreadPoolHHBUI&) = delete;
        ThreadPoolHHBUI& operator=(const ThreadPoolHHBUI&) = delete;
        ThreadPoolHHBUI(ThreadPoolHHBUI&&) = delete;
        ThreadPoolHHBUI& operator=(ThreadPoolHHBUI&&) = delete;
        
        template<typename F, typename... Args>
        auto submit(F&& f, Args&&... args) 
            -> std::future<std::invoke_result_t<F, Args...>> {
            
            using ReturnType = std::invoke_result_t<F, Args...>;
            
            auto task = std::make_shared<std::packaged_task<ReturnType()>>(
                std::bind(std::forward<F>(f), std::forward<Args>(args)...)
            );
            
            auto future = task->get_future();
            
            {
                std::unique_lock lock(queueMutex_);
                if (stop_) {
                    throw std::runtime_error("ThreadPool is stopped");
                }
                
                tasks_.emplace([task] { (*task)(); });
            }
            
            condition_.notify_one();
            return future;
        }
        
        void shutdown() {
            {
                std::unique_lock lock(queueMutex_);
                stop_ = true;
            }
            
            condition_.notify_all();
            
            for (auto& worker : workers_) {
                if (worker.joinable()) {
                    worker.join();
                }
            }
            
            workers_.clear();
        }
        
        std::size_t getThreadCount() const {
            return workers_.size();
        }
        
        std::size_t getQueueSize() const {
            std::unique_lock lock(queueMutex_);
            return tasks_.size();
        }
        
    private:
        void workerLoop() {
            while (true) {
                std::function<void()> task;
                
                {
                    std::unique_lock lock(queueMutex_);
                    condition_.wait(lock, [this] { return stop_ || !tasks_.empty(); });
                    
                    if (stop_ && tasks_.empty()) {
                        return;
                    }
                    
                    task = std::move(tasks_.front());
                    tasks_.pop();
                }
                
                try {
                    task();
                } catch (...) {
                    // 异常安全：继续处理其他任务
                }
            }
        }
        
        std::vector<std::thread> workers_;
        std::queue<std::function<void()>> tasks_;
        
        mutable std::mutex queueMutex_;
        std::condition_variable condition_;
        std::atomic<bool> stop_;
    };
    
    // 线程安全的事件系统
    template<typename... Args>
    class EventHHBUI {
    public:
        using HandlerType = std::function<void(Args...)>;
        using HandlerIdType = std::uint64_t;
        
        HandlerIdType subscribe(HandlerType handler) {
            std::unique_lock lock(mutex_);
            auto id = nextId_++;
            handlers_[id] = std::move(handler);
            return id;
        }
        
        bool unsubscribe(HandlerIdType id) {
            std::unique_lock lock(mutex_);
            return handlers_.erase(id) > 0;
        }
        
        void fire(Args... args) {
            std::shared_lock lock(mutex_);
            for (const auto& [id, handler] : handlers_) {
                try {
                    handler(args...);
                } catch (...) {
                    // 异常安全：继续通知其他处理器
                }
            }
        }
        
        void clear() {
            std::unique_lock lock(mutex_);
            handlers_.clear();
        }
        
        std::size_t getHandlerCount() const {
            std::shared_lock lock(mutex_);
            return handlers_.size();
        }
        
    private:
        mutable std::shared_mutex mutex_;
        std::unordered_map<HandlerIdType, HandlerType> handlers_;
        std::atomic<HandlerIdType> nextId_{1};
    };
    
    // 线程本地存储管理器
    template<typename T>
    class ThreadLocalStorageHHBUI {
    public:
        ThreadLocalStorageHHBUI() = default;
        
        template<typename... Args>
        T& get(Args&&... args) {
            if (!storage_.get()) {
                storage_.reset(new T(std::forward<Args>(args)...));
            }
            return *storage_;
        }
        
        void reset() {
            storage_.reset();
        }
        
        bool hasValue() const {
            return storage_.get() != nullptr;
        }
        
    private:
        thread_local static std::unique_ptr<T> storage_;
    };
    
    template<typename T>
    thread_local std::unique_ptr<T> ThreadLocalStorageHHBUI<T>::storage_;
    
    // 异步任务执行器
    class AsyncExecutorHHBUI {
    public:
        static AsyncExecutorHHBUI& getInstance() {
            static AsyncExecutorHHBUI instance;
            return instance;
        }
        
        template<typename F, typename... Args>
        auto executeAsync(F&& f, Args&&... args) 
            -> std::future<std::invoke_result_t<F, Args...>> {
            return threadPool_.submit(std::forward<F>(f), std::forward<Args>(args)...);
        }
        
        template<typename F>
        void executeDelayed(std::chrono::milliseconds delay, F&& f) {
            auto task = [f = std::forward<F>(f), delay]() {
                std::this_thread::sleep_for(delay);
                f();
            };
            threadPool_.submit(std::move(task));
        }
        
        template<typename F>
        void executeRepeated(std::chrono::milliseconds interval, F&& f, std::shared_ptr<std::atomic<bool>> stopFlag) {
            auto task = [f = std::forward<F>(f), interval, stopFlag]() {
                while (!stopFlag->load()) {
                    f();
                    std::this_thread::sleep_for(interval);
                }
            };
            threadPool_.submit(std::move(task));
        }
        
        void shutdown() {
            threadPool_.shutdown();
        }
        
    private:
        AsyncExecutorHHBUI() : threadPool_(std::thread::hardware_concurrency()) {}
        
        ThreadPoolHHBUI threadPool_;
    };
    
    // 读写锁包装器
    class ReadWriteLockHHBUI {
    public:
        class ReadLock {
        public:
            explicit ReadLock(ReadWriteLockHHBUI& rwLock) : lock_(rwLock.mutex_) {}
        private:
            std::shared_lock<std::shared_mutex> lock_;
        };
        
        class WriteLock {
        public:
            explicit WriteLock(ReadWriteLockHHBUI& rwLock) : lock_(rwLock.mutex_) {}
        private:
            std::unique_lock<std::shared_mutex> lock_;
        };
        
        ReadLock acquireReadLock() { return ReadLock(*this); }
        WriteLock acquireWriteLock() { return WriteLock(*this); }
        
    private:
        std::shared_mutex mutex_;
        friend class ReadLock;
        friend class WriteLock;
    };
    
    // 原子计数器
    class AtomicCounterHHBUI {
    public:
        AtomicCounterHHBUI(std::int64_t initial = 0) : value_(initial) {}
        
        std::int64_t increment() {
            return value_.fetch_add(1, std::memory_order_relaxed) + 1;
        }
        
        std::int64_t decrement() {
            return value_.fetch_sub(1, std::memory_order_relaxed) - 1;
        }
        
        std::int64_t get() const {
            return value_.load(std::memory_order_relaxed);
        }
        
        void set(std::int64_t value) {
            value_.store(value, std::memory_order_relaxed);
        }
        
        std::int64_t exchange(std::int64_t value) {
            return value_.exchange(value, std::memory_order_relaxed);
        }
        
        bool compareExchange(std::int64_t expected, std::int64_t desired) {
            return value_.compare_exchange_weak(expected, desired, std::memory_order_relaxed);
        }
        
    private:
        std::atomic<std::int64_t> value_;
    };
    
} // namespace HHBUIPro
