# HHBUI Pro - 现代化C++17 UI框架

## 项目概述

HHBUI Pro 是对原始HHBUI框架的完全现代化重构版本，采用现代C++17标准，提供高性能、类型安全、异常安全的UI开发体验。

### 主要特性

- **现代C++17标准**: 采用智能指针、RAII、移动语义、完美转发等现代特性
- **高性能渲染**: DirectX11/Direct2D混合渲染引擎，GPU硬件加速
- **线程安全**: 全面的多线程安全设计，支持并发UI操作
- **内存安全**: 智能内存管理，自动资源回收，内存泄漏检测
- **异常安全**: 强异常安全保证，RAII自动资源管理
- **模块化架构**: 清晰的模块分离，易于扩展和维护
- **现代化命名**: 驼峰命名法+LLVM风格，避免与系统API冲突

## 架构设计

### 核心模块

```
HHBUI_Pro/
├── Core/                   # 核心基础模块
│   ├── Base.hpp           # 基础类型与COM接口
│   ├── Memory.hpp         # 内存管理与对象池
│   └── Threading.hpp      # 线程安全与并发
├── Engine/                # 渲染引擎模块
│   └── RenderEngine.hpp  # DirectX11/D2D渲染
├── Controls/              # UI控件模块
│   ├── ControlBase.hpp    # 控件基础架构
│   └── ButtonHHBUI.hpp    # 现代化按钮控件
├── Application/           # 应用程序框架
├── Common/               # 通用工具模块
└── ThirdParty/           # 第三方库集成
```

### 技术栈

- **语言标准**: C++17
- **编译器**: Visual Studio 2022 (v143)
- **渲染API**: DirectX 11, Direct2D, DirectWrite
- **平台**: Windows 10+
- **架构**: x86, x64

## 重大改进

### 1. 现代化类型系统

```cpp
// 旧版本 - 类型不安全
using UIimage = LPVOID;
using UIbrush = LPVOID;

// 新版本 - 强类型安全
template<typename T>
using ComPtr = Microsoft::WRL::ComPtr<T>;
using RenderDeviceHHBUI = ComPtr<ID3D11Device>;
```

### 2. 智能内存管理

```cpp
// 旧版本 - 手动内存管理
LPVOID ExMemAlloc(size_t size);
void ExMemFree(LPVOID ptr);

// 新版本 - RAII自动管理
template<typename T>
using UniquePtr = std::unique_ptr<T>;
template<typename T>
using SharedPtr = std::shared_ptr<T>;
```

### 3. 现代化事件系统

```cpp
// 旧版本 - C风格回调
using EventHandlerPROC = LRESULT(CALLBACK*)(LPVOID, LPVOID, INT, INT, WPARAM, LPARAM);

// 新版本 - 现代化函数对象
using ControlEventHandlerHHBUI = std::function<void(ControlEventArgsHHBUI&)>;
```

### 4. 线程安全设计

```cpp
// 旧版本 - 简单临界区
class ExLock {
    CRITICAL_SECTION m_cs;
};

// 新版本 - 现代化同步原语
class ThreadPoolHHBUI {
    std::shared_mutex mutex_;
    std::condition_variable condition_;
    std::atomic<bool> stop_;
};
```

## 使用示例

### 基本窗口创建

```cpp
#include "HHBUIPro.hpp"
using namespace HHBUIPro;

int main() {
    // 框架自动初始化
    auto renderDevice = Factory::CreateRenderDevice();
    
    // 创建按钮
    auto button = ButtonFactory::CreatePrimaryButton(L"Click Me!");
    button->setClickHandler([](ButtonClickEventArgsHHBUI& args) {
        MessageBoxW(nullptr, L"Button Clicked!", L"Info", MB_OK);
    });
    
    return 0;
}
```

### 现代化控件使用

```cpp
// 创建不同样式的按钮
auto primaryBtn = ButtonFactory::CreatePrimaryButton(L"Primary");
auto toggleBtn = ButtonFactory::CreateToggleButton(L"Toggle", false);
auto iconBtn = ButtonFactory::CreateIconButton(L"icon.png", L"Icon");

// 设置事件处理器
primaryBtn->setClickHandler([](auto& args) {
    // 处理点击事件
});

toggleBtn->setToggleHandler([](bool toggled) {
    // 处理切换状态
});
```

## 编译要求

### 系统要求
- Windows 10 或更高版本
- Visual Studio 2022 或更高版本
- Windows 10 SDK

### 编译配置
- C++17 标准 (`/std:c++17`)
- Unicode 字符集
- 多处理器编译 (`/MP`)
- 优化设置：Release模式启用最大速度优化

### 依赖库
- d3d11.lib - DirectX 11
- d2d1.lib - Direct2D
- dwrite.lib - DirectWrite
- windowscodecs.lib - Windows Imaging Component
- dxgi.lib - DirectX Graphics Infrastructure

## 性能优化

### 1. 内存池化
- 高性能对象池，减少内存分配开销
- 智能内存对齐，提升缓存命中率
- 自适应池大小调整

### 2. 渲染优化
- GPU硬件加速渲染
- 渲染状态缓存
- 批量绘制调用优化

### 3. 多线程优化
- 无锁数据结构
- 线程池任务调度
- 读写锁优化

## 调试与诊断

### 内存泄漏检测
```cpp
// Debug模式自动启用
#if HHBUI_DEBUG
    EnableMemoryLeakDetection(true);
#endif
```

### 性能监控
```cpp
// 性能测量宏
HHBUI_PROFILE_FUNCTION();
HHBUI_PROFILE_SCOPE("CustomOperation");
```

### 日志系统
```cpp
// 调试输出
HHBUI_TRACE(L"Debug message");
HHBUI_TRACEF(L"Formatted: %d", value);
```

## 兼容性说明

### 不兼容性变更
- **完全不兼容旧版本**: 这是一个全新的现代化实现
- **API重新设计**: 采用现代化命名约定和类型系统
- **内存管理变更**: 从手动管理转向智能指针
- **事件系统重构**: 从C风格回调转向现代化函数对象

### 迁移指南
1. 更新包含头文件：`#include "HHBUIPro.hpp"`
2. 使用新的命名空间：`using namespace HHBUIPro;`
3. 替换旧的类型定义为新的强类型
4. 更新事件处理器为现代化函数对象
5. 使用智能指针替代原始指针

## 版本信息

- **当前版本**: *******
- **API版本**: 1
- **构建日期**: 2025-07-30
- **最低Windows版本**: Windows 10
- **最低Visual Studio版本**: 2022

## 许可证

版权所有 (c) 2025 HHBUI Pro
保留所有权利。

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 技术支持

- 官方网站: https://hhbui.com/
- 文档: https://docs.hhbui.com/
- 问题反馈: https://github.com/hhbui/hhbui-pro/issues

---

**注意**: 这是HHBUI框架的现代化重构版本，专注于性能、安全性和可维护性的全面提升。
