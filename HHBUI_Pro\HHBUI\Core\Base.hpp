/**
** =====================================================================================
**
**       文件名称: Base.hpp
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI Pro】现代化C++17核心基础类 - 高性能UI框架基础架构
**
**       主要功能:
**       - 现代化C++17基础类型定义与接口设计
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误处理机制
**       - 高性能内存管理与对象池化
**       - 线程安全的引用计数与生命周期管理
**       - 模板元编程与类型安全设计
**       - COM接口兼容与现代C++融合
**
**       技术特性:
**       - 采用现代C++17标准（智能指针、移动语义、完美转发）
**       - RAII自动资源管理与异常安全保证
**       - 无锁编程与高性能原子操作
**       - 模板元编程与编译期优化
**       - 类型安全与强类型系统设计
**       - 现代化命名约定（驼峰命名法+LLVM风格）
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化C++17基础架构
**                             2. 实现智能指针与RAII管理
**                             3. 添加异常安全保证机制
**                             4. 集成高性能内存管理
**                             5. 实现线程安全引用计数
**
** =====================================================================================
*/

#pragma once

#include <memory>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <type_traits>
#include <utility>
#include <functional>
#include <string>
#include <string_view>
#include <optional>
#include <variant>
#include <any>
#include <chrono>
#include <thread>
#include <future>
#include <exception>
#include <stdexcept>

// Windows平台特定头文件
#define NOMINMAX
#include <Windows.h>
#include <combaseapi.h>

// 现代化命名约定与API设计
namespace HHBUIPro {
    
    // 前向声明
    class WindowHHBUI;
    class ControlHHBUI;
    class CanvasHHBUI;
    class LayoutHHBUI;
    class AnimationHHBUI;
    
    // 现代化类型别名（强类型设计）
    using WindowHandleHHBUI = HWND;
    using ControlIdHHBUI = std::uint32_t;
    using TimestampHHBUI = std::chrono::steady_clock::time_point;
    using DurationHHBUI = std::chrono::milliseconds;
    
    // 智能指针类型别名
    template<typename T>
    using UniquePtr = std::unique_ptr<T>;
    
    template<typename T>
    using SharedPtr = std::shared_ptr<T>;
    
    template<typename T>
    using WeakPtr = std::weak_ptr<T>;
    
    // 现代化错误处理
    enum class ErrorCodeHHBUI : std::uint32_t {
        Success = 0,
        InvalidParameter,
        OutOfMemory,
        NotInitialized,
        AlreadyInitialized,
        NotFound,
        AccessDenied,
        ThreadingError,
        RenderingError,
        Unknown = 0xFFFFFFFF
    };
    
    // 异常安全的结果类型
    template<typename T>
    using ResultHHBUI = std::variant<T, ErrorCodeHHBUI>;
    
    // 现代化异常类
    class ExceptionHHBUI : public std::runtime_error {
    public:
        explicit ExceptionHHBUI(ErrorCodeHHBUI code, std::string_view message = "")
            : std::runtime_error(std::string(message))
            , errorCode_(code) {}
        
        ErrorCodeHHBUI getErrorCode() const noexcept { return errorCode_; }
        
    private:
        ErrorCodeHHBUI errorCode_;
    };
    
    // 现代化COM接口基类（线程安全引用计数）
    class ComObjectHHBUI {
    public:
        ComObjectHHBUI() noexcept : refCount_(1) {}
        virtual ~ComObjectHHBUI() = default;
        
        // 禁用拷贝，允许移动
        ComObjectHHBUI(const ComObjectHHBUI&) = delete;
        ComObjectHHBUI& operator=(const ComObjectHHBUI&) = delete;
        ComObjectHHBUI(ComObjectHHBUI&&) = default;
        ComObjectHHBUI& operator=(ComObjectHHBUI&&) = default;
        
        // 线程安全的引用计数
        std::uint32_t addRef() noexcept {
            return refCount_.fetch_add(1, std::memory_order_relaxed) + 1;
        }
        
        std::uint32_t release() noexcept {
            const auto count = refCount_.fetch_sub(1, std::memory_order_acq_rel) - 1;
            if (count == 0) {
                delete this;
            }
            return count;
        }
        
        std::uint32_t getRefCount() const noexcept {
            return refCount_.load(std::memory_order_relaxed);
        }
        
        // 现代化COM接口查询
        template<typename InterfaceType>
        ResultHHBUI<InterfaceType*> queryInterface() noexcept {
            if constexpr (std::is_base_of_v<InterfaceType, std::remove_pointer_t<decltype(this)>>) {
                addRef();
                return static_cast<InterfaceType*>(this);
            } else {
                return ErrorCodeHHBUI::NotFound;
            }
        }
        
    private:
        std::atomic<std::uint32_t> refCount_;
    };
    
    // 现代化智能COM指针
    template<typename T>
    class ComPtrHHBUI {
    public:
        ComPtrHHBUI() noexcept = default;
        
        explicit ComPtrHHBUI(T* ptr) noexcept : ptr_(ptr) {
            if (ptr_) ptr_->addRef();
        }
        
        ComPtrHHBUI(const ComPtrHHBUI& other) noexcept : ptr_(other.ptr_) {
            if (ptr_) ptr_->addRef();
        }
        
        ComPtrHHBUI(ComPtrHHBUI&& other) noexcept : ptr_(std::exchange(other.ptr_, nullptr)) {}
        
        ~ComPtrHHBUI() noexcept {
            if (ptr_) ptr_->release();
        }
        
        ComPtrHHBUI& operator=(const ComPtrHHBUI& other) noexcept {
            if (this != &other) {
                ComPtrHHBUI temp(other);
                swap(temp);
            }
            return *this;
        }
        
        ComPtrHHBUI& operator=(ComPtrHHBUI&& other) noexcept {
            if (this != &other) {
                ComPtrHHBUI temp(std::move(other));
                swap(temp);
            }
            return *this;
        }
        
        T* get() const noexcept { return ptr_; }
        T* operator->() const noexcept { return ptr_; }
        T& operator*() const noexcept { return *ptr_; }
        
        explicit operator bool() const noexcept { return ptr_ != nullptr; }
        
        void reset(T* ptr = nullptr) noexcept {
            ComPtrHHBUI temp(ptr);
            swap(temp);
        }
        
        T* release() noexcept {
            return std::exchange(ptr_, nullptr);
        }
        
        void swap(ComPtrHHBUI& other) noexcept {
            std::swap(ptr_, other.ptr_);
        }
        
        // 类型安全的接口查询
        template<typename U>
        ComPtrHHBUI<U> queryInterface() const noexcept {
            if (!ptr_) return {};
            
            auto result = ptr_->template queryInterface<U>();
            if (std::holds_alternative<U*>(result)) {
                auto* interface = std::get<U*>(result);
                return ComPtrHHBUI<U>(interface);
            }
            return {};
        }
        
    private:
        T* ptr_ = nullptr;
    };
    
    // 现代化基础对象类
    class BaseObjectHHBUI : public ComObjectHHBUI {
    public:
        BaseObjectHHBUI() = default;
        virtual ~BaseObjectHHBUI() = default;
        
        // 获取对象类型信息
        virtual std::string_view getTypeName() const noexcept = 0;
        virtual std::uint32_t getTypeId() const noexcept = 0;
        
        // 线程安全的属性访问
        template<typename T>
        void setProperty(std::string_view name, T&& value) {
            std::unique_lock lock(propertiesMutex_);
            properties_[std::string(name)] = std::forward<T>(value);
        }
        
        template<typename T>
        std::optional<T> getProperty(std::string_view name) const {
            std::shared_lock lock(propertiesMutex_);
            auto it = properties_.find(std::string(name));
            if (it != properties_.end()) {
                try {
                    return std::any_cast<T>(it->second);
                } catch (const std::bad_any_cast&) {
                    return std::nullopt;
                }
            }
            return std::nullopt;
        }
        
        // 事件处理机制
        using EventHandlerHHBUI = std::function<void(const std::any&)>;
        
        void addEventListener(std::string_view eventName, EventHandlerHHBUI handler) {
            std::unique_lock lock(eventsMutex_);
            eventHandlers_[std::string(eventName)].emplace_back(std::move(handler));
        }
        
        void removeEventListener(std::string_view eventName) {
            std::unique_lock lock(eventsMutex_);
            eventHandlers_.erase(std::string(eventName));
        }
        
        void fireEvent(std::string_view eventName, const std::any& eventData = {}) {
            std::shared_lock lock(eventsMutex_);
            auto it = eventHandlers_.find(std::string(eventName));
            if (it != eventHandlers_.end()) {
                for (const auto& handler : it->second) {
                    try {
                        handler(eventData);
                    } catch (...) {
                        // 异常安全：继续处理其他事件处理器
                    }
                }
            }
        }
        
    private:
        mutable std::shared_mutex propertiesMutex_;
        std::unordered_map<std::string, std::any> properties_;
        
        mutable std::shared_mutex eventsMutex_;
        std::unordered_map<std::string, std::vector<EventHandlerHHBUI>> eventHandlers_;
    };
    
} // namespace HHBUIPro
