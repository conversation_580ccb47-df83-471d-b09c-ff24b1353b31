/**
** =====================================================================================
**
**       文件名称: RenderEngine.hpp
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI Pro】现代化C++17渲染引擎 - 高性能DirectX11/D2D混合渲染系统
**
**       主要功能:
**       - 现代化C++17高性能渲染引擎架构
**       - DirectX11与Direct2D混合渲染管线
**       - GPU硬件加速与着色器管理系统
**       - 高级纹理与缓冲区资源管理
**       - 实时渲染性能监控与调试
**       - 多线程渲染与命令队列系统
**       - 现代化图形API抽象层设计
**
**       技术特性:
**       - 采用现代C++17标准与DirectX11 API
**       - RAII自动资源管理与异常安全保证
**       - 智能指针与COM接口管理
**       - 高性能渲染状态缓存系统
**       - 多线程安全的渲染命令队列
**       - 实时性能监控与统计分析
**       - 可扩展的渲染管线架构
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化渲染引擎架构
**                             2. 实现DirectX11混合渲染
**                             3. 添加智能资源管理系统
**                             4. 集成多线程渲染支持
**                             5. 实现性能监控框架
**
** =====================================================================================
*/

#pragma once

#include "../Core/Base.hpp"
#include "../Core/Memory.hpp"
#include "../Core/Threading.hpp"
#include <d3d11.h>
#include <d2d1.h>
#include <dwrite.h>
#include <wincodec.h>
#include <wrl/client.h>
#include <DirectXMath.h>
#include <vector>
#include <unordered_map>
#include <queue>
#include <chrono>

namespace HHBUIPro::Engine {
    
    using namespace Microsoft::WRL;
    using namespace DirectX;
    
    // 现代化渲染资源管理
    template<typename T>
    using ComPtr = Microsoft::WRL::ComPtr<T>;
    
    // 渲染统计信息
    struct RenderStatsHHBUI {
        std::atomic<std::uint64_t> frameCount{0};
        std::atomic<std::uint64_t> drawCalls{0};
        std::atomic<std::uint64_t> triangleCount{0};
        std::atomic<std::uint64_t> textureBinds{0};
        std::atomic<std::uint64_t> shaderSwitches{0};
        std::atomic<std::uint64_t> bufferUpdates{0};
        
        std::atomic<float> frameTime{0.0f};
        std::atomic<float> cpuTime{0.0f};
        std::atomic<float> gpuTime{0.0f};
        
        void reset() noexcept {
            frameCount = 0;
            drawCalls = 0;
            triangleCount = 0;
            textureBinds = 0;
            shaderSwitches = 0;
            bufferUpdates = 0;
            frameTime = 0.0f;
            cpuTime = 0.0f;
            gpuTime = 0.0f;
        }
        
        float getFPS() const noexcept {
            auto ft = frameTime.load();
            return ft > 0.0f ? 1000.0f / ft : 0.0f;
        }
    };
    
    // 渲染设备配置
    struct RenderDeviceConfigHHBUI {
        bool enableDebugLayer = false;
        bool enableMultisampling = true;
        UINT multisampleCount = 4;
        UINT multisampleQuality = 0;
        bool enableVSync = true;
        bool enableHardwareAcceleration = true;
        DXGI_FORMAT backBufferFormat = DXGI_FORMAT_R8G8B8A8_UNORM;
        DXGI_FORMAT depthStencilFormat = DXGI_FORMAT_D24_UNORM_S8_UINT;
    };
    
    // 渲染命令接口
    class IRenderCommandHHBUI {
    public:
        virtual ~IRenderCommandHHBUI() = default;
        virtual void execute(ID3D11DeviceContext* context) = 0;
        virtual std::string_view getCommandName() const noexcept = 0;
    };
    
    // 渲染命令队列
    class RenderCommandQueueHHBUI {
    public:
        void submitCommand(std::unique_ptr<IRenderCommandHHBUI> command) {
            HHBUI_SCOPED_LOCK(mutex_);
            commands_.push(std::move(command));
        }
        
        void executeCommands(ID3D11DeviceContext* context) {
            std::queue<std::unique_ptr<IRenderCommandHHBUI>> localQueue;
            
            {
                HHBUI_SCOPED_LOCK(mutex_);
                localQueue.swap(commands_);
            }
            
            while (!localQueue.empty()) {
                auto& command = localQueue.front();
                try {
                    command->execute(context);
                } catch (const std::exception& e) {
                    // 记录错误但继续执行其他命令
                    OutputDebugStringA(("Render command failed: " + std::string(e.what()) + "\n").c_str());
                }
                localQueue.pop();
            }
        }
        
        std::size_t getCommandCount() const {
            HHBUI_SCOPED_LOCK(mutex_);
            return commands_.size();
        }
        
        void clear() {
            HHBUI_SCOPED_LOCK(mutex_);
            std::queue<std::unique_ptr<IRenderCommandHHBUI>> empty;
            commands_.swap(empty);
        }
        
    private:
        mutable std::mutex mutex_;
        std::queue<std::unique_ptr<IRenderCommandHHBUI>> commands_;
    };
    
    // 现代化渲染设备
    class RenderDeviceHHBUI : public BaseObjectHHBUI {
    public:
        explicit RenderDeviceHHBUI(const RenderDeviceConfigHHBUI& config = {});
        ~RenderDeviceHHBUI() override;
        
        // 禁用拷贝，允许移动
        RenderDeviceHHBUI(const RenderDeviceHHBUI&) = delete;
        RenderDeviceHHBUI& operator=(const RenderDeviceHHBUI&) = delete;
        RenderDeviceHHBUI(RenderDeviceHHBUI&&) = default;
        RenderDeviceHHBUI& operator=(RenderDeviceHHBUI&&) = default;
        
        // BaseObjectHHBUI 接口实现
        std::string_view getTypeName() const noexcept override { return "RenderDeviceHHBUI"; }
        std::uint32_t getTypeId() const noexcept override { return 0x52444556; } // 'RDEV'
        
        // 设备初始化与清理
        ResultHHBUI<void> initialize(HWND hwnd, UINT width, UINT height);
        void shutdown() noexcept;
        
        // 渲染控制
        void beginFrame();
        void endFrame();
        void present();
        
        // 资源访问
        ID3D11Device* getD3DDevice() const noexcept { return d3dDevice_.Get(); }
        ID3D11DeviceContext* getD3DContext() const noexcept { return d3dContext_.Get(); }
        ID2D1RenderTarget* getD2DRenderTarget() const noexcept { return d2dRenderTarget_.Get(); }
        IDWriteFactory* getDWriteFactory() const noexcept { return dwriteFactory_.Get(); }
        
        // 渲染状态管理
        void setViewport(const D3D11_VIEWPORT& viewport);
        void setRenderTarget(ID3D11RenderTargetView* rtv, ID3D11DepthStencilView* dsv = nullptr);
        void clearRenderTarget(const float clearColor[4]);
        void clearDepthStencil(float depth = 1.0f, UINT8 stencil = 0);
        
        // 命令队列
        RenderCommandQueueHHBUI& getCommandQueue() { return commandQueue_; }
        
        // 性能监控
        const RenderStatsHHBUI& getStats() const noexcept { return stats_; }
        void resetStats() noexcept { stats_.reset(); }
        
        // 设备能力查询
        bool isFeatureSupported(D3D11_FEATURE feature) const;
        DXGI_FORMAT getSupportedFormat(const std::vector<DXGI_FORMAT>& candidates) const;
        
        // 资源创建辅助方法
        ResultHHBUI<ComPtr<ID3D11Buffer>> createBuffer(const D3D11_BUFFER_DESC& desc, const D3D11_SUBRESOURCE_DATA* initialData = nullptr);
        ResultHHBUI<ComPtr<ID3D11Texture2D>> createTexture2D(const D3D11_TEXTURE2D_DESC& desc, const D3D11_SUBRESOURCE_DATA* initialData = nullptr);
        ResultHHBUI<ComPtr<ID3D11ShaderResourceView>> createShaderResourceView(ID3D11Resource* resource, const D3D11_SHADER_RESOURCE_VIEW_DESC* desc = nullptr);
        ResultHHBUI<ComPtr<ID3D11RenderTargetView>> createRenderTargetView(ID3D11Resource* resource, const D3D11_RENDER_TARGET_VIEW_DESC* desc = nullptr);
        
    private:
        // 初始化辅助方法
        ResultHHBUI<void> createD3DDevice();
        ResultHHBUI<void> createSwapChain(HWND hwnd, UINT width, UINT height);
        ResultHHBUI<void> createD2DResources();
        ResultHHBUI<void> createRenderTargets();
        
        // 配置与状态
        RenderDeviceConfigHHBUI config_;
        bool initialized_ = false;
        
        // DirectX 资源
        ComPtr<ID3D11Device> d3dDevice_;
        ComPtr<ID3D11DeviceContext> d3dContext_;
        ComPtr<IDXGISwapChain> swapChain_;
        ComPtr<ID3D11RenderTargetView> backBufferRTV_;
        ComPtr<ID3D11DepthStencilView> depthStencilView_;
        ComPtr<ID3D11Texture2D> depthStencilBuffer_;
        
        // Direct2D 资源
        ComPtr<ID2D1Factory> d2dFactory_;
        ComPtr<ID2D1RenderTarget> d2dRenderTarget_;
        ComPtr<IDWriteFactory> dwriteFactory_;
        ComPtr<IWICImagingFactory> wicFactory_;
        
        // 渲染状态
        D3D11_VIEWPORT currentViewport_{};
        
        // 命令队列与统计
        RenderCommandQueueHHBUI commandQueue_;
        mutable RenderStatsHHBUI stats_;
        
        // 性能计时
        std::chrono::high_resolution_clock::time_point frameStartTime_;
        std::chrono::high_resolution_clock::time_point cpuStartTime_;
        
        // 线程安全
        mutable std::shared_mutex deviceMutex_;
    };
    
    // 渲染上下文
    class RenderContextHHBUI {
    public:
        explicit RenderContextHHBUI(SharedPtr<RenderDeviceHHBUI> device)
            : device_(std::move(device)) {}
        
        // 绘制操作
        void drawPrimitive(D3D11_PRIMITIVE_TOPOLOGY topology, UINT vertexCount, UINT startVertex = 0);
        void drawIndexed(D3D11_PRIMITIVE_TOPOLOGY topology, UINT indexCount, UINT startIndex = 0, INT baseVertex = 0);
        void drawInstanced(D3D11_PRIMITIVE_TOPOLOGY topology, UINT vertexCountPerInstance, UINT instanceCount, UINT startVertex = 0, UINT startInstance = 0);
        
        // 状态设置
        void setVertexBuffer(ID3D11Buffer* buffer, UINT stride, UINT offset = 0);
        void setIndexBuffer(ID3D11Buffer* buffer, DXGI_FORMAT format, UINT offset = 0);
        void setConstantBuffer(UINT slot, ID3D11Buffer* buffer, D3D11_BIND_FLAG bindFlag);
        void setTexture(UINT slot, ID3D11ShaderResourceView* srv);
        void setSampler(UINT slot, ID3D11SamplerState* sampler);
        
        // 着色器设置
        void setVertexShader(ID3D11VertexShader* shader, ID3D11InputLayout* inputLayout = nullptr);
        void setPixelShader(ID3D11PixelShader* shader);
        void setGeometryShader(ID3D11GeometryShader* shader);
        void setHullShader(ID3D11HullShader* shader);
        void setDomainShader(ID3D11DomainShader* shader);
        void setComputeShader(ID3D11ComputeShader* shader);
        
        // 渲染状态
        void setBlendState(ID3D11BlendState* state, const float blendFactor[4] = nullptr, UINT sampleMask = 0xffffffff);
        void setDepthStencilState(ID3D11DepthStencilState* state, UINT stencilRef = 0);
        void setRasterizerState(ID3D11RasterizerState* state);
        
        // 设备访问
        RenderDeviceHHBUI* getDevice() const { return device_.get(); }
        
    private:
        SharedPtr<RenderDeviceHHBUI> device_;
    };
    
    // 全局渲染管理器
    class RenderManagerHHBUI {
    public:
        static RenderManagerHHBUI& getInstance() {
            static RenderManagerHHBUI instance;
            return instance;
        }
        
        ResultHHBUI<SharedPtr<RenderDeviceHHBUI>> createDevice(const RenderDeviceConfigHHBUI& config = {});
        void destroyDevice(SharedPtr<RenderDeviceHHBUI> device);
        
        SharedPtr<RenderDeviceHHBUI> getDefaultDevice() const {
            HHBUI_SCOPED_SHARED_LOCK(devicesMutex_);
            return defaultDevice_;
        }
        
        void setDefaultDevice(SharedPtr<RenderDeviceHHBUI> device) {
            HHBUI_SCOPED_LOCK(devicesMutex_);
            defaultDevice_ = std::move(device);
        }
        
        std::vector<SharedPtr<RenderDeviceHHBUI>> getAllDevices() const {
            HHBUI_SCOPED_SHARED_LOCK(devicesMutex_);
            return devices_;
        }
        
        void shutdown();
        
    private:
        RenderManagerHHBUI() = default;
        
        mutable std::shared_mutex devicesMutex_;
        std::vector<SharedPtr<RenderDeviceHHBUI>> devices_;
        SharedPtr<RenderDeviceHHBUI> defaultDevice_;
    };
    
} // namespace HHBUIPro::Engine
