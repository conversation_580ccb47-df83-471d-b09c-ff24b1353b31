﻿/**
** =====================================================================================
**
**       文件名称: pch.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI Pro】预编译头文件 - 现代化C++17 UI框架公共头文件
**
**       主要功能:
**       - 现代化C++17标准库头文件预编译
**       - Windows平台API与DirectX头文件包含
**       - 第三方库头文件统一管理
**       - 编译优化与性能提升配置
**       - 全局宏定义与类型别名
**       - 平台兼容性与版本控制
**       - 调试与发布模式配置
**
**       技术特性:
**       - 采用现代C++17标准库
**       - Windows 10+ 平台支持
**       - DirectX 11/Direct2D 集成
**       - 高性能编译优化
**       - 内存泄漏检测支持
**       - 多线程安全配置
**       - 异常安全保证
**
**       更新记录:
**       2025-07-30 v******* : 1. 创建现代化预编译头文件
**                             2. 集成C++17标准库支持
**                             3. 添加DirectX平台集成
**                             4. 实现编译优化配置
**                             5. 统一全局类型定义
**
** =====================================================================================
*/

#pragma once

// 目标平台版本定义
#ifndef WINVER
#define WINVER 0x0A00          // Windows 10
#endif

#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0A00    // Windows 10
#endif

#ifndef _WIN32_WINDOWS
#define _WIN32_WINDOWS 0x0A00  // Windows 10
#endif

#ifndef _WIN32_IE
#define _WIN32_IE 0x0A00       // Internet Explorer 10.0
#endif

// 编译器优化与警告控制
#pragma warning(push)
#pragma warning(disable: 4996) // 禁用已弃用函数警告
#pragma warning(disable: 4251) // 禁用DLL接口警告
#pragma warning(disable: 4275) // 禁用DLL基类警告

// Windows头文件优化
#define WIN32_LEAN_AND_MEAN    // 排除很少使用的Windows头文件
#define NOMINMAX               // 防止Windows.h定义min/max宏
#define NODRAWTEXT             // 排除DrawText相关定义
#define NOGDI                  // 排除GDI相关定义
#define NOBITMAP               // 排除位图相关定义
#define NOMCX                  // 排除MCX相关定义
#define NOSERVICE              // 排除服务相关定义
#define NOHELP                 // 排除帮助相关定义

// C++17标准库头文件
#include <memory>
#include <string>
#include <string_view>
#include <vector>
#include <array>
#include <unordered_map>
#include <unordered_set>
#include <map>
#include <set>
#include <queue>
#include <stack>
#include <deque>
#include <list>
#include <forward_list>
#include <algorithm>
#include <functional>
#include <utility>
#include <type_traits>
#include <optional>
#include <variant>
#include <any>
#include <tuple>
#include <initializer_list>

// 数值与数学库
#include <cmath>
#include <numeric>
#include <random>
#include <limits>
#include <cstdint>
#include <cstddef>
#include <climits>
#include <cfloat>

// 时间与日期库
#include <chrono>
#include <ctime>

// 输入输出库
#include <iostream>
#include <iomanip>
#include <sstream>
#include <fstream>
#include <streambuf>

// 并发与线程库
#include <thread>
#include <mutex>
#include <shared_mutex>
#include <condition_variable>
#include <atomic>
#include <future>
#include <asyncinfo.h>
// 异常处理库
#include <exception>
#include <stdexcept>
#include <system_error>

// 内存管理库
#include <memory_resource>
#include <new>
#include <cstdlib>
#include <cstring>

// Windows平台头文件
#include <Windows.h>
#include <WindowsX.h>
#include <CommCtrl.h>
#include <ShellAPI.h>
#include <ShlObj.h>
#include <combaseapi.h>
#include <objbase.h>
#include <unknwn.h>

// DirectX头文件
#include <d3d11.h>
#include <d3d11_1.h>
#include <d2d1.h>
#include <d2d1_1.h>
#include <dwrite.h>
#include <dwrite_1.h>
#include <wincodec.h>
#include <dxgi.h>
#include <dxgi1_2.h>
#include <DirectXMath.h>
#include <DirectXColors.h>

// Windows Runtime Library (WRL)
#include <wrl/client.h>
#include <wrl/implements.h>
#include <wrl/module.h>
#include <wrl/event.h>

// 恢复警告设置
#pragma warning(pop)

// 全局宏定义
#ifdef _DEBUG
    #define HHBUI_DEBUG 1
    #define HHBUI_ENABLE_MEMORY_LEAK_DETECTION 1
#else
    #define HHBUI_DEBUG 0
    #define HHBUI_ENABLE_MEMORY_LEAK_DETECTION 0
#endif

// API导出/导入宏
#ifdef HHBUI_PRO_EXPORTS
    #define HHBUI_API __declspec(dllexport)
#else
    #define HHBUI_API __declspec(dllimport)
#endif

// 调用约定宏
#define HHBUI_CALL __stdcall
#define HHBUI_CALLBACK __stdcall

// 内联宏
#define HHBUI_INLINE __forceinline
#define HHBUI_NOINLINE __declspec(noinline)

// 对齐宏
#define HHBUI_ALIGN(n) __declspec(align(n))
#define HHBUI_CACHE_ALIGN HHBUI_ALIGN(64)

// 异常规范宏
#define HHBUI_NOEXCEPT noexcept
#define HHBUI_NOEXCEPT_IF(cond) noexcept(cond)

// 弃用宏
#define HHBUI_DEPRECATED [[deprecated]]
#define HHBUI_DEPRECATED_MSG(msg) [[deprecated(msg)]]

// 可能未使用宏
#define HHBUI_MAYBE_UNUSED [[maybe_unused]]

// 版本信息
#define HHBUI_PRO_VERSION_MAJOR 2
#define HHBUI_PRO_VERSION_MINOR 0
#define HHBUI_PRO_VERSION_PATCH 0
#define HHBUI_PRO_VERSION_BUILD 0

#define HHBUI_PRO_VERSION_STRING L"*******"
#define HHBUI_PRO_VERSION_STRINGA "*******"

// 编译时版本检查
#define HHBUI_PRO_VERSION_NUMBER \
    ((HHBUI_PRO_VERSION_MAJOR << 24) | \
     (HHBUI_PRO_VERSION_MINOR << 16) | \
     (HHBUI_PRO_VERSION_PATCH << 8) | \
     HHBUI_PRO_VERSION_BUILD)

// 全局类型别名
namespace HHBUIPro {
    // 基础类型别名
    using byte = std::uint8_t;
    using int8 = std::int8_t;
    using uint8 = std::uint8_t;
    using int16 = std::int16_t;
    using uint16 = std::uint16_t;
    using int32 = std::int32_t;
    using uint32 = std::uint32_t;
    using int64 = std::int64_t;
    using uint64 = std::uint64_t;
    using float32 = float;
    using float64 = double;

    // 字符串类型别名
    using String = std::wstring;
    using StringView = std::wstring_view;
    using StringA = std::string;
    using StringViewA = std::string_view;

    // 智能指针类型别名
    template<typename T>
    using UniquePtr = std::unique_ptr<T>;

    template<typename T>
    using SharedPtr = std::shared_ptr<T>;

    template<typename T>
    using WeakPtr = std::weak_ptr<T>;

    // 函数类型别名
    template<typename T>
    using Function = std::function<T>;

    // 容器类型别名
    template<typename T>
    using Vector = std::vector<T>;

    template<typename T, std::size_t N>
    using Array = std::array<T, N>;

    template<typename K, typename V>
    using Map = std::unordered_map<K, V>;

    template<typename T>
    using Set = std::unordered_set<T>;

    // 时间类型别名
    using TimePoint = std::chrono::steady_clock::time_point;
    using Duration = std::chrono::milliseconds;
    using Clock = std::chrono::steady_clock;

    // DirectX类型别名
    using namespace DirectX;
    template<typename T>
    using ComPtr = Microsoft::WRL::ComPtr<T>;
}

// 内存泄漏检测（仅Debug模式）
#if HHBUI_ENABLE_MEMORY_LEAK_DETECTION
    #define _CRTDBG_MAP_ALLOC
    #include <crtdbg.h>

    #ifdef _DEBUG
        #ifndef DBG_NEW
            #define DBG_NEW new ( _NORMAL_BLOCK , __FILE__ , __LINE__ )
            #define new DBG_NEW
        #endif
    #endif
#endif

// 全局初始化函数声明
namespace HHBUIPro {
    // 框架初始化与清理
    HHBUI_API bool InitializeFramework() HHBUI_NOEXCEPT;
    HHBUI_API void ShutdownFramework() HHBUI_NOEXCEPT;

    // 版本信息获取
    HHBUI_API uint32 GetVersionNumber() HHBUI_NOEXCEPT;
    HHBUI_API const wchar_t* GetVersionString() HHBUI_NOEXCEPT;

    // 调试与诊断
    HHBUI_API void EnableMemoryLeakDetection(bool enable = true) HHBUI_NOEXCEPT;
    HHBUI_API void DumpMemoryLeaks() HHBUI_NOEXCEPT;
}
