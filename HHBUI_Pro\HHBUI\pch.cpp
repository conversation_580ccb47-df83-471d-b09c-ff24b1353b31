﻿/**
** =====================================================================================
**
**       文件名称: pch.cpp
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI Pro】预编译头实现文件 - 现代化C++17 UI框架初始化
**
**       主要功能:
**       - 预编译头文件生成与编译优化
**       - 框架全局初始化与清理管理
**       - 内存泄漏检测与调试支持
**       - 版本信息与兼容性检查
**       - COM组件初始化与管理
**       - DirectX设备初始化准备
**       - 全局资源管理与清理
**
**       技术特性:
**       - 现代化C++17初始化模式
**       - RAII自动资源管理
**       - 异常安全的初始化流程
**       - 线程安全的全局状态管理
**       - 高性能编译优化支持
**       - 内存使用监控与统计
**       - 平台兼容性检查
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化预编译头实现
**                             2. 实现框架初始化管理
**                             3. 添加内存泄漏检测
**                             4. 集成版本控制系统
**                             5. 实现全局资源管理
**
** =====================================================================================
*/

#include "pch.h"
#include <atomic>
#include <mutex>

namespace HHBUIPro {
    
    // 全局状态管理
    namespace {
        std::atomic<bool> g_frameworkInitialized{false};
        std::atomic<bool> g_memoryLeakDetectionEnabled{false};
        std::mutex g_initializationMutex;
        
        // COM初始化状态
        std::atomic<bool> g_comInitialized{false};
        
        // 初始化计数器（支持多次初始化/清理）
        std::atomic<int32> g_initializationCount{0};
    }
    
    // 框架初始化函数
    HHBUI_API bool InitializeFramework() HHBUI_NOEXCEPT {
        try {
            std::lock_guard<std::mutex> lock(g_initializationMutex);
            
            // 增加初始化计数
            const auto count = g_initializationCount.fetch_add(1) + 1;
            
            // 如果已经初始化过，直接返回成功
            if (count > 1) {
                return true;
            }
            
            // 启用内存泄漏检测（Debug模式）
            #if HHBUI_ENABLE_MEMORY_LEAK_DETECTION
                _CrtSetDbgFlag(_CRTDBG_ALLOC_MEM_DF | _CRTDBG_LEAK_CHECK_DF);
                _CrtSetReportMode(_CRT_WARN, _CRTDBG_MODE_DEBUG);
                _CrtSetReportMode(_CRT_ERROR, _CRTDBG_MODE_DEBUG);
                _CrtSetReportMode(_CRT_ASSERT, _CRTDBG_MODE_DEBUG);
                g_memoryLeakDetectionEnabled.store(true);
            #endif
            
            // 初始化COM组件
            HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED | COINIT_DISABLE_OLE1DDE);
            if (SUCCEEDED(hr)) {
                g_comInitialized.store(true);
            } else if (hr == RPC_E_CHANGED_MODE) {
                // COM已经以不同模式初始化，这通常是可以接受的
                g_comInitialized.store(true);
            } else {
                // COM初始化失败
                g_initializationCount.fetch_sub(1);
                return false;
            }
            
            // 设置DPI感知（Windows 10+）
            if (IsWindows10OrGreater()) {
                SetThreadDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);
            } else {
                SetProcessDPIAware();
            }
            
            // 初始化通用控件库
            INITCOMMONCONTROLSEX icex = {};
            icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
            icex.dwICC = ICC_WIN95_CLASSES | ICC_STANDARD_CLASSES | ICC_USEREX_CLASSES;
            InitCommonControlsEx(&icex);
            
            // 标记框架已初始化
            g_frameworkInitialized.store(true);
            
            return true;
            
        } catch (...) {
            // 异常安全：确保计数器正确
            g_initializationCount.fetch_sub(1);
            return false;
        }
    }
    
    // 框架清理函数
    HHBUI_API void ShutdownFramework() HHBUI_NOEXCEPT {
        try {
            std::lock_guard<std::mutex> lock(g_initializationMutex);
            
            // 减少初始化计数
            const auto count = g_initializationCount.fetch_sub(1) - 1;
            
            // 如果还有其他地方在使用框架，不进行清理
            if (count > 0) {
                return;
            }
            
            // 确保计数不会变成负数
            if (count < 0) {
                g_initializationCount.store(0);
                return;
            }
            
            // 标记框架未初始化
            g_frameworkInitialized.store(false);
            
            // 清理COM组件
            if (g_comInitialized.exchange(false)) {
                CoUninitialize();
            }
            
            // 输出内存泄漏报告（Debug模式）
            #if HHBUI_ENABLE_MEMORY_LEAK_DETECTION
                if (g_memoryLeakDetectionEnabled.load()) {
                    _CrtDumpMemoryLeaks();
                    g_memoryLeakDetectionEnabled.store(false);
                }
            #endif
            
        } catch (...) {
            // 清理过程中的异常应该被忽略
        }
    }
    
    // 获取版本号
    HHBUI_API uint32 GetVersionNumber() HHBUI_NOEXCEPT {
        return HHBUI_PRO_VERSION_NUMBER;
    }
    
    // 获取版本字符串
    HHBUI_API const wchar_t* GetVersionString() HHBUI_NOEXCEPT {
        return HHBUI_PRO_VERSION_STRING;
    }
    
    // 启用/禁用内存泄漏检测
    HHBUI_API void EnableMemoryLeakDetection(bool enable) HHBUI_NOEXCEPT {
        #if HHBUI_ENABLE_MEMORY_LEAK_DETECTION
            if (enable) {
                _CrtSetDbgFlag(_CRTDBG_ALLOC_MEM_DF | _CRTDBG_LEAK_CHECK_DF);
                g_memoryLeakDetectionEnabled.store(true);
            } else {
                _CrtSetDbgFlag(_CRTDBG_ALLOC_MEM_DF);
                g_memoryLeakDetectionEnabled.store(false);
            }
        #else
            // Release模式下忽略此调用
            (void)enable;
        #endif
    }
    
    // 输出内存泄漏信息
    HHBUI_API void DumpMemoryLeaks() HHBUI_NOEXCEPT {
        #if HHBUI_ENABLE_MEMORY_LEAK_DETECTION
            if (g_memoryLeakDetectionEnabled.load()) {
                _CrtDumpMemoryLeaks();
            }
        #endif
    }
    
    // Windows版本检查辅助函数
    namespace {
        bool IsWindows10OrGreater() HHBUI_NOEXCEPT {
            OSVERSIONINFOEXW osvi = {};
            osvi.dwOSVersionInfoSize = sizeof(osvi);
            osvi.dwMajorVersion = 10;
            osvi.dwMinorVersion = 0;
            
            DWORDLONG conditionMask = 0;
            VER_SET_CONDITION(conditionMask, VER_MAJORVERSION, VER_GREATER_EQUAL);
            VER_SET_CONDITION(conditionMask, VER_MINORVERSION, VER_GREATER_EQUAL);
            
            return VerifyVersionInfoW(&osvi, VER_MAJORVERSION | VER_MINORVERSION, conditionMask) != FALSE;
        }
    }
    
    // 全局构造/析构管理器
    namespace {
        struct FrameworkManager {
            FrameworkManager() {
                // 在静态初始化时自动初始化框架
                InitializeFramework();
            }
            
            ~FrameworkManager() {
                // 在程序退出时自动清理框架
                ShutdownFramework();
            }
        };
        
        // 静态实例，确保框架在程序启动时初始化，退出时清理
        [[maybe_unused]] static FrameworkManager g_frameworkManager;
    }
    
} // namespace HHBUIPro
