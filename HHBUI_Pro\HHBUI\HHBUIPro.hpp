/**
** =====================================================================================
**
**       文件名称: HHBUIPro.hpp
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI Pro】现代化C++17 UI框架主头文件 - 统一API接口
**
**       主要功能:
**       - 现代化C++17 UI框架统一API接口
**       - 高性能渲染引擎与控件系统集成
**       - 智能内存管理与线程安全机制
**       - 现代化事件处理与消息路由
**       - 可扩展的控件库与布局系统
**       - 高级动画与过渡效果支持
**       - 跨平台兼容与性能优化
**
**       技术特性:
**       - 采用现代C++17标准与最佳实践
**       - RAII自动资源管理与异常安全保证
**       - 智能指针与COM接口管理
**       - 高性能DirectX11/D2D混合渲染
**       - 多线程安全的事件系统
**       - 现代化命名约定与API设计
**       - 模块化架构与可扩展性
**
**       使用示例:
**       ```cpp
**       #include "HHBUIPro.hpp"
**       using namespace HHBUIPro;
**       
**       int main() {
**           // 框架自动初始化
**           auto window = std::make_unique<WindowHHBUI>();
**           window->create(L"HHBUI Pro Demo", 800, 600);
**           
**           auto button = std::make_unique<ButtonHHBUI>();
**           button->setText(L"Click Me");
**           window->addChild(std::move(button));
**           
**           return window->runMessageLoop();
**       }
**       ```
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化UI框架主接口
**                             2. 集成所有核心模块
**                             3. 实现统一API设计
**                             4. 添加使用示例与文档
**                             5. 完成模块化架构
**
** =====================================================================================
*/

#pragma once

// 预编译头文件
#include "pch.h"

// 核心模块
#include "Core/Base.hpp"
#include "Core/Memory.hpp"
#include "Core/Threading.hpp"

// 渲染引擎
#include "Engine/RenderEngine.hpp"

// 控件系统
#include "Controls/ControlBase.hpp"

// 版本信息与兼容性
#define HHBUI_PRO_API_VERSION 1

// 命名空间别名，便于使用
namespace HHBUI = HHBUIPro;

/**
 * @brief HHBUI Pro 现代化C++17 UI框架
 * 
 * 这是一个完全重构的现代化UI框架，采用C++17标准，提供：
 * - 高性能DirectX11/D2D渲染引擎
 * - 现代化控件系统与布局管理
 * - 智能内存管理与线程安全
 * - 异常安全的API设计
 * - 可扩展的架构模式
 */
namespace HHBUIPro {
    
    // 导出核心类型到主命名空间
    using namespace HHBUIPro::Engine;
    using namespace HHBUIPro::Controls;
    
    // 框架信息结构
    struct FrameworkInfoHHBUI {
        uint32 versionNumber = HHBUI_PRO_VERSION_NUMBER;
        const wchar_t* versionString = HHBUI_PRO_VERSION_STRING;
        const wchar_t* buildDate = __DATE__;
        const wchar_t* buildTime = __TIME__;
        bool debugBuild = HHBUI_DEBUG;
        bool memoryLeakDetection = HHBUI_ENABLE_MEMORY_LEAK_DETECTION;
    };
    
    /**
     * @brief 获取框架信息
     * @return 框架版本与构建信息
     */
    HHBUI_API FrameworkInfoHHBUI GetFrameworkInfo() HHBUI_NOEXCEPT;
    
    /**
     * @brief 检查API兼容性
     * @param requiredVersion 要求的最低API版本
     * @return 是否兼容
     */
    HHBUI_API bool IsAPICompatible(uint32 requiredVersion) HHBUI_NOEXCEPT;
    
    /**
     * @brief 设置全局日志级别
     * @param level 日志级别 (0=关闭, 1=错误, 2=警告, 3=信息, 4=调试)
     */
    HHBUI_API void SetLogLevel(uint32 level) HHBUI_NOEXCEPT;
    
    /**
     * @brief 输出调试信息
     * @param message 调试消息
     */
    HHBUI_API void DebugOutput(const wchar_t* message) HHBUI_NOEXCEPT;
    
    /**
     * @brief 输出格式化调试信息
     * @param format 格式字符串
     * @param ... 参数列表
     */
    HHBUI_API void DebugOutputF(const wchar_t* format, ...) HHBUI_NOEXCEPT;
    
    // 便利宏定义
    #if HHBUI_DEBUG
        #define HHBUI_TRACE(msg) HHBUIPro::DebugOutput(msg)
        #define HHBUI_TRACEF(fmt, ...) HHBUIPro::DebugOutputF(fmt, __VA_ARGS__)
    #else
        #define HHBUI_TRACE(msg) ((void)0)
        #define HHBUI_TRACEF(fmt, ...) ((void)0)
    #endif
    
    // 错误处理宏
    #define HHBUI_VERIFY(expr) \
        do { \
            if (!(expr)) { \
                HHBUI_TRACEF(L"Verification failed: %s at %s:%d", \
                    L#expr, __FILEW__, __LINE__); \
                __debugbreak(); \
            } \
        } while (0)
    
    #define HHBUI_ASSERT(expr) \
        do { \
            if (!(expr)) { \
                HHBUI_TRACEF(L"Assertion failed: %s at %s:%d", \
                    L#expr, __FILEW__, __LINE__); \
                std::terminate(); \
            } \
        } while (0)
    
    // 性能测量宏
    #if HHBUI_DEBUG
        #define HHBUI_PROFILE_SCOPE(name) \
            auto HHBUI_CONCAT(_prof_, __LINE__) = \
                HHBUIPro::ProfileScope(name)
        
        #define HHBUI_PROFILE_FUNCTION() \
            HHBUI_PROFILE_SCOPE(__FUNCTIONW__)
    #else
        #define HHBUI_PROFILE_SCOPE(name) ((void)0)
        #define HHBUI_PROFILE_FUNCTION() ((void)0)
    #endif
    
    // 性能测量辅助类
    class ProfileScope {
    public:
        explicit ProfileScope(const wchar_t* name) HHBUI_NOEXCEPT
            : name_(name), start_(Clock::now()) {
            HHBUI_TRACEF(L"[PROFILE] Begin: %s", name_);
        }
        
        ~ProfileScope() HHBUI_NOEXCEPT {
            auto end = Clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start_);
            HHBUI_TRACEF(L"[PROFILE] End: %s (%.3f ms)", 
                name_, duration.count() / 1000.0);
        }
        
    private:
        const wchar_t* name_;
        TimePoint start_;
    };
    
    // 全局配置结构
    struct GlobalConfigHHBUI {
        // 渲染配置
        bool enableHardwareAcceleration = true;
        bool enableVSync = true;
        uint32 maxFrameRate = 60;
        
        // 内存配置
        size_t memoryPoolSize = 64 * 1024 * 1024; // 64MB
        bool enableMemoryPooling = true;
        
        // 线程配置
        uint32 workerThreadCount = 0; // 0 = 自动检测
        bool enableMultithreading = true;
        
        // 调试配置
        bool enableDebugOutput = HHBUI_DEBUG;
        bool enablePerformanceMonitoring = HHBUI_DEBUG;
        uint32 logLevel = HHBUI_DEBUG ? 4 : 1;
    };
    
    /**
     * @brief 设置全局配置
     * @param config 配置结构
     * @return 是否设置成功
     */
    HHBUI_API bool SetGlobalConfig(const GlobalConfigHHBUI& config) HHBUI_NOEXCEPT;
    
    /**
     * @brief 获取全局配置
     * @return 当前配置
     */
    HHBUI_API GlobalConfigHHBUI GetGlobalConfig() HHBUI_NOEXCEPT;
    
    // 快速创建函数
    namespace Factory {
        /**
         * @brief 创建渲染设备
         * @param config 设备配置
         * @return 渲染设备智能指针
         */
        HHBUI_API SharedPtr<RenderDeviceHHBUI> CreateRenderDevice(
            const RenderDeviceConfigHHBUI& config = {});
        
        /**
         * @brief 创建内存分配器
         * @param poolConfigs 内存池配置
         * @return 内存分配器智能指针
         */
        HHBUI_API UniquePtr<MemoryAllocatorHHBUI> CreateMemoryAllocator(
            const Vector<std::pair<size_t, size_t>>& poolConfigs = {});
        
        /**
         * @brief 创建线程池
         * @param threadCount 线程数量（0=自动检测）
         * @return 线程池智能指针
         */
        HHBUI_API UniquePtr<ThreadPoolHHBUI> CreateThreadPool(
            size_t threadCount = 0);
    }
    
    // 实用工具函数
    namespace Utils {
        /**
         * @brief 获取系统DPI缩放比例
         * @return DPI缩放比例
         */
        HHBUI_API float GetSystemDPIScale() HHBUI_NOEXCEPT;
        
        /**
         * @brief 将逻辑像素转换为物理像素
         * @param logicalPixels 逻辑像素值
         * @return 物理像素值
         */
        HHBUI_API float LogicalToPhysicalPixels(float logicalPixels) HHBUI_NOEXCEPT;
        
        /**
         * @brief 将物理像素转换为逻辑像素
         * @param physicalPixels 物理像素值
         * @return 逻辑像素值
         */
        HHBUI_API float PhysicalToLogicalPixels(float physicalPixels) HHBUI_NOEXCEPT;
        
        /**
         * @brief 获取系统颜色主题
         * @return true=深色主题, false=浅色主题
         */
        HHBUI_API bool IsSystemDarkTheme() HHBUI_NOEXCEPT;
        
        /**
         * @brief 获取系统动画启用状态
         * @return 是否启用动画
         */
        HHBUI_API bool IsSystemAnimationEnabled() HHBUI_NOEXCEPT;
    }
    
} // namespace HHBUIPro

// 全局操作符重载
namespace HHBUIPro::Controls {
    // 颜色预定义常量实现
    inline const ColorHHBUI ColorHHBUI::Transparent{0.0f, 0.0f, 0.0f, 0.0f};
    inline const ColorHHBUI ColorHHBUI::Black{0.0f, 0.0f, 0.0f, 1.0f};
    inline const ColorHHBUI ColorHHBUI::White{1.0f, 1.0f, 1.0f, 1.0f};
    inline const ColorHHBUI ColorHHBUI::Red{1.0f, 0.0f, 0.0f, 1.0f};
    inline const ColorHHBUI ColorHHBUI::Green{0.0f, 1.0f, 0.0f, 1.0f};
    inline const ColorHHBUI ColorHHBUI::Blue{0.0f, 0.0f, 1.0f, 1.0f};
    inline const ColorHHBUI ColorHHBUI::Yellow{1.0f, 1.0f, 0.0f, 1.0f};
    inline const ColorHHBUI ColorHHBUI::Cyan{0.0f, 1.0f, 1.0f, 1.0f};
    inline const ColorHHBUI ColorHHBUI::Magenta{1.0f, 0.0f, 1.0f, 1.0f};
}

// 便利的using声明
using HHBUIFrameworkInfo = HHBUIPro::FrameworkInfoHHBUI;
using HHBUIGlobalConfig = HHBUIPro::GlobalConfigHHBUI;
using HHBUIRenderDevice = HHBUIPro::RenderDeviceHHBUI;
using HHBUIControl = HHBUIPro::IControlHHBUI;
using HHBUIColor = HHBUIPro::Controls::ColorHHBUI;
using HHBUIRect = HHBUIPro::Controls::RectHHBUI;
using HHBUISize = HHBUIPro::Controls::SizeHHBUI;
using HHBUIPoint = HHBUIPro::Controls::PointHHBUI;
