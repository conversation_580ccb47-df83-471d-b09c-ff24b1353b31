/**
** =====================================================================================
**
**       文件名称: ButtonHHBUI.hpp
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI Pro】现代化C++17按钮控件 - 高性能UI按钮组件
**
**       主要功能:
**       - 现代化C++17按钮控件实现
**       - 高性能事件处理与状态管理
**       - 智能样式系统与主题支持
**       - 现代化动画与过渡效果
**       - 可自定义的渲染管线
**       - 无障碍访问支持
**       - 触摸与手势识别
**
**       技术特性:
**       - 采用现代C++17标准与智能指针技术
**       - RAII自动资源管理与异常安全保证
**       - 高性能状态机与事件分发
**       - GPU加速渲染与缓存优化
**       - 线程安全的属性访问
**       - 现代化命名约定与API设计
**       - 可组合的控件架构模式
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化按钮控件
**                             2. 实现高性能事件处理
**                             3. 添加智能样式系统
**                             4. 集成动画效果支持
**                             5. 实现无障碍访问
**
** =====================================================================================
*/

#pragma once

#include "ControlBase.hpp"
#include "../Engine/RenderEngine.hpp"
#include <functional>
#include <string>
#include <chrono>

namespace HHBUIPro::Controls {
    
    // 按钮样式枚举
    enum class ButtonStyleHHBUI : std::uint32_t {
        Default = 0,        // 默认样式
        Primary,            // 主要按钮
        Secondary,          // 次要按钮
        Success,            // 成功样式
        Warning,            // 警告样式
        Danger,             // 危险样式
        Info,               // 信息样式
        Link,               // 链接样式
        Outline,            // 轮廓样式
        Ghost,              // 幽灵样式
        Custom              // 自定义样式
    };
    
    // 按钮类型枚举
    enum class ButtonTypeHHBUI : std::uint32_t {
        Normal = 0,         // 普通按钮
        Toggle,             // 切换按钮
        Radio,              // 单选按钮
        Checkbox,           // 复选框按钮
        Split,              // 分割按钮
        Dropdown            // 下拉按钮
    };
    
    // 按钮图标位置
    enum class ButtonIconPositionHHBUI : std::uint32_t {
        None = 0,           // 无图标
        Left,               // 左侧
        Right,              // 右侧
        Top,                // 顶部
        Bottom,             // 底部
        Center              // 居中（仅图标）
    };
    
    // 按钮点击事件参数
    struct ButtonClickEventArgsHHBUI : public ControlEventArgsHHBUI {
        bool isToggled = false;         // 切换状态（仅切换按钮）
        std::uint32_t clickCount = 1;   // 点击次数
        
        ButtonClickEventArgsHHBUI() : ControlEventArgsHHBUI(ControlEventTypeHHBUI::MouseClick) {}
    };
    
    // 按钮样式配置
    struct ButtonStyleConfigHHBUI {
        // 背景颜色（正常、悬停、按下、禁用）
        std::array<ColorHHBUI, 4> backgroundColor = {
            ColorHHBUI::White,
            ColorHHBUI(0.9f, 0.9f, 0.9f, 1.0f),
            ColorHHBUI(0.8f, 0.8f, 0.8f, 1.0f),
            ColorHHBUI(0.7f, 0.7f, 0.7f, 1.0f)
        };
        
        // 边框颜色
        std::array<ColorHHBUI, 4> borderColor = {
            ColorHHBUI(0.7f, 0.7f, 0.7f, 1.0f),
            ColorHHBUI(0.5f, 0.5f, 0.5f, 1.0f),
            ColorHHBUI(0.3f, 0.3f, 0.3f, 1.0f),
            ColorHHBUI(0.6f, 0.6f, 0.6f, 1.0f)
        };
        
        // 文本颜色
        std::array<ColorHHBUI, 4> textColor = {
            ColorHHBUI::Black,
            ColorHHBUI::Black,
            ColorHHBUI::Black,
            ColorHHBUI(0.5f, 0.5f, 0.5f, 1.0f)
        };
        
        // 几何属性
        float borderWidth = 1.0f;
        float cornerRadius = 4.0f;
        SizeHHBUI padding = {8.0f, 4.0f};
        SizeHHBUI margin = {2.0f, 2.0f};
        
        // 动画属性
        DurationHHBUI hoverTransitionDuration = std::chrono::milliseconds(150);
        DurationHHBUI pressTransitionDuration = std::chrono::milliseconds(100);
        
        // 阴影属性
        bool enableShadow = true;
        ColorHHBUI shadowColor = ColorHHBUI(0.0f, 0.0f, 0.0f, 0.2f);
        PointHHBUI shadowOffset = {2.0f, 2.0f};
        float shadowBlurRadius = 4.0f;
    };
    
    // 现代化按钮控件类
    class HHBUI_API ButtonHHBUI : public BaseObjectHHBUI, public IControlHHBUI {
    public:
        // 构造函数
        explicit ButtonHHBUI(ControlIdHHBUI id = InvalidControlId);
        ~ButtonHHBUI() override = default;
        
        // 禁用拷贝，允许移动
        ButtonHHBUI(const ButtonHHBUI&) = delete;
        ButtonHHBUI& operator=(const ButtonHHBUI&) = delete;
        ButtonHHBUI(ButtonHHBUI&&) = default;
        ButtonHHBUI& operator=(ButtonHHBUI&&) = default;
        
        // BaseObjectHHBUI 接口实现
        std::string_view getTypeName() const noexcept override { return "ButtonHHBUI"; }
        std::uint32_t getTypeId() const noexcept override { return 0x42544E48; } // 'BTNH'
        
        // IControlHHBUI 接口实现
        ControlIdHHBUI getId() const noexcept override { return id_; }
        std::string_view getName() const noexcept override { return name_; }
        void setName(std::string_view name) override { name_ = std::string(name); }
        
        RectHHBUI getBounds() const noexcept override { return bounds_; }
        void setBounds(const RectHHBUI& bounds) override;
        PointHHBUI getPosition() const noexcept override { return bounds_.getPosition(); }
        void setPosition(const PointHHBUI& position) override;
        SizeHHBUI getSize() const noexcept override { return bounds_.getSize(); }
        void setSize(const SizeHHBUI& size) override;
        
        ControlStateHHBUI getState() const noexcept override { return state_; }
        void setState(ControlStateHHBUI state) override;
        bool hasState(ControlStateHHBUI state) const noexcept override;
        void addState(ControlStateHHBUI state) override;
        void removeState(ControlStateHHBUI state) override;
        
        bool isVisible() const noexcept override;
        void setVisible(bool visible) override;
        bool isEnabled() const noexcept override;
        void setEnabled(bool enabled) override;
        
        IControlHHBUI* getParent() const noexcept override { return parent_; }
        void setParent(IControlHHBUI* parent) override { parent_ = parent; }
        std::vector<IControlHHBUI*> getChildren() const override { return {}; } // 按钮无子控件
        void addChild(UniquePtr<IControlHHBUI> child) override { /* 按钮不支持子控件 */ }
        UniquePtr<IControlHHBUI> removeChild(IControlHHBUI* child) override { return nullptr; }
        
        void addEventListener(ControlEventTypeHHBUI eventType, ControlEventHandlerHHBUI handler) override;
        void removeEventListener(ControlEventTypeHHBUI eventType) override;
        void fireEvent(UniquePtr<ControlEventArgsHHBUI> args) override;
        
        void render(Engine::RenderContextHHBUI& context, const RectHHBUI& clipRect) override;
        void invalidate(const RectHHBUI& rect = {}) override;
        
        SizeHHBUI measureDesiredSize(const SizeHHBUI& availableSize) override;
        void arrange(const RectHHBUI& finalRect) override;
        
        IControlHHBUI* hitTest(const PointHHBUI& point) override;
        
        void setProperty(std::string_view name, std::any value) override;
        std::optional<std::any> getProperty(std::string_view name) const override;
        
        // 按钮特有方法
        const String& getText() const noexcept { return text_; }
        void setText(const String& text);
        
        ButtonStyleHHBUI getButtonStyle() const noexcept { return buttonStyle_; }
        void setButtonStyle(ButtonStyleHHBUI style);
        
        ButtonTypeHHBUI getButtonType() const noexcept { return buttonType_; }
        void setButtonType(ButtonTypeHHBUI type);
        
        ButtonIconPositionHHBUI getIconPosition() const noexcept { return iconPosition_; }
        void setIconPosition(ButtonIconPositionHHBUI position);
        
        const ButtonStyleConfigHHBUI& getStyleConfig() const noexcept { return styleConfig_; }
        void setStyleConfig(const ButtonStyleConfigHHBUI& config);
        
        bool isToggled() const noexcept { return toggled_; }
        void setToggled(bool toggled);
        
        // 事件处理器设置
        void setClickHandler(std::function<void(ButtonClickEventArgsHHBUI&)> handler);
        void setToggleHandler(std::function<void(bool)> handler);
        
        // 动画控制
        void startHoverAnimation();
        void startPressAnimation();
        void startReleaseAnimation();
        
        // 无障碍访问
        String getAccessibilityText() const;
        void setAccessibilityText(const String& text);
        
    private:
        // 内部方法
        void updateVisualState();
        void renderBackground(Engine::RenderContextHHBUI& context, const RectHHBUI& rect);
        void renderBorder(Engine::RenderContextHHBUI& context, const RectHHBUI& rect);
        void renderText(Engine::RenderContextHHBUI& context, const RectHHBUI& rect);
        void renderIcon(Engine::RenderContextHHBUI& context, const RectHHBUI& rect);
        void renderShadow(Engine::RenderContextHHBUI& context, const RectHHBUI& rect);
        
        ColorHHBUI getCurrentBackgroundColor() const;
        ColorHHBUI getCurrentBorderColor() const;
        ColorHHBUI getCurrentTextColor() const;
        
        void handleMouseEnter(MouseEventArgsHHBUI& args);
        void handleMouseLeave(MouseEventArgsHHBUI& args);
        void handleMouseDown(MouseEventArgsHHBUI& args);
        void handleMouseUp(MouseEventArgsHHBUI& args);
        void handleMouseClick(MouseEventArgsHHBUI& args);
        
        // 成员变量
        ControlIdHHBUI id_;
        std::string name_;
        RectHHBUI bounds_;
        ControlStateHHBUI state_ = ControlStateHHBUI::Normal | ControlStateHHBUI::Visible;
        IControlHHBUI* parent_ = nullptr;
        
        // 按钮属性
        String text_;
        ButtonStyleHHBUI buttonStyle_ = ButtonStyleHHBUI::Default;
        ButtonTypeHHBUI buttonType_ = ButtonTypeHHBUI::Normal;
        ButtonIconPositionHHBUI iconPosition_ = ButtonIconPositionHHBUI::None;
        ButtonStyleConfigHHBUI styleConfig_;
        bool toggled_ = false;
        
        // 事件处理
        std::unordered_map<ControlEventTypeHHBUI, ControlEventHandlerHHBUI> eventHandlers_;
        std::function<void(ButtonClickEventArgsHHBUI&)> clickHandler_;
        std::function<void(bool)> toggleHandler_;
        
        // 动画状态
        TimePoint lastStateChange_;
        float animationProgress_ = 0.0f;
        bool isAnimating_ = false;
        
        // 无障碍访问
        String accessibilityText_;
        
        // 线程安全
        mutable std::shared_mutex stateMutex_;
    };
    
    // 按钮工厂函数
    namespace ButtonFactory {
        /**
         * @brief 创建标准按钮
         * @param text 按钮文本
         * @param style 按钮样式
         * @return 按钮智能指针
         */
        HHBUI_API UniquePtr<ButtonHHBUI> CreateButton(
            const String& text = L"Button",
            ButtonStyleHHBUI style = ButtonStyleHHBUI::Default);
        
        /**
         * @brief 创建主要按钮
         * @param text 按钮文本
         * @return 按钮智能指针
         */
        HHBUI_API UniquePtr<ButtonHHBUI> CreatePrimaryButton(const String& text = L"Primary");
        
        /**
         * @brief 创建切换按钮
         * @param text 按钮文本
         * @param initialState 初始切换状态
         * @return 按钮智能指针
         */
        HHBUI_API UniquePtr<ButtonHHBUI> CreateToggleButton(
            const String& text = L"Toggle",
            bool initialState = false);
        
        /**
         * @brief 创建图标按钮
         * @param iconPath 图标路径
         * @param text 按钮文本（可选）
         * @return 按钮智能指针
         */
        HHBUI_API UniquePtr<ButtonHHBUI> CreateIconButton(
            const String& iconPath,
            const String& text = L"");
    }
    
} // namespace HHBUIPro::Controls
