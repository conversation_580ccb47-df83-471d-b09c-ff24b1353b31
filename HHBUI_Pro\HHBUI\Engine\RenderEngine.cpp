/**
** =====================================================================================
**
**       文件名称: RenderEngine.cpp
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI Pro】现代化C++17渲染引擎实现
**
** =====================================================================================
*/

#include "pch.h"
#include "RenderEngine.hpp"
#include <dxgi1_2.h>

namespace HHBUIPro::Engine {
    
    // RenderDeviceHHBUI 实现
    RenderDeviceHHBUI::RenderDeviceHHBUI(const RenderDeviceConfigHHBUI& config)
        : config_(config) {
    }
    
    RenderDeviceHHBUI::~RenderDeviceHHBUI() {
        shutdown();
    }
    
    ResultHHBUI<void> RenderDeviceHHBUI::initialize(HWND hwnd, UINT width, UINT height) {
        HHBUI_SCOPED_LOCK(deviceMutex_);
        
        if (initialized_) {
            return ErrorCodeHHBUI::AlreadyInitialized;
        }
        
        try {
            // 创建D3D设备
            auto result = createD3DDevice();
            if (std::holds_alternative<ErrorCodeHHBUI>(result)) {
                return std::get<ErrorCodeHHBUI>(result);
            }
            
            // 创建交换链
            result = createSwapChain(hwnd, width, height);
            if (std::holds_alternative<ErrorCodeHHBUI>(result)) {
                return std::get<ErrorCodeHHBUI>(result);
            }
            
            // 创建D2D资源
            result = createD2DResources();
            if (std::holds_alternative<ErrorCodeHHBUI>(result)) {
                return std::get<ErrorCodeHHBUI>(result);
            }
            
            // 创建渲染目标
            result = createRenderTargets();
            if (std::holds_alternative<ErrorCodeHHBUI>(result)) {
                return std::get<ErrorCodeHHBUI>(result);
            }
            
            // 设置默认视口
            D3D11_VIEWPORT viewport = {};
            viewport.Width = static_cast<FLOAT>(width);
            viewport.Height = static_cast<FLOAT>(height);
            viewport.MinDepth = 0.0f;
            viewport.MaxDepth = 1.0f;
            setViewport(viewport);
            
            initialized_ = true;
            return {};
            
        } catch (const std::exception&) {
            return ErrorCodeHHBUI::Unknown;
        }
    }
    
    void RenderDeviceHHBUI::shutdown() noexcept {
        HHBUI_SCOPED_LOCK(deviceMutex_);
        
        if (!initialized_) {
            return;
        }
        
        // 清理命令队列
        commandQueue_.clear();
        
        // 释放D2D资源
        wicFactory_.Reset();
        dwriteFactory_.Reset();
        d2dRenderTarget_.Reset();
        d2dFactory_.Reset();
        
        // 释放D3D资源
        depthStencilBuffer_.Reset();
        depthStencilView_.Reset();
        backBufferRTV_.Reset();
        swapChain_.Reset();
        d3dContext_.Reset();
        d3dDevice_.Reset();
        
        initialized_ = false;
    }
    
    void RenderDeviceHHBUI::beginFrame() {
        frameStartTime_ = std::chrono::high_resolution_clock::now();
        cpuStartTime_ = frameStartTime_;
        
        stats_.frameCount.fetch_add(1, std::memory_order_relaxed);
    }
    
    void RenderDeviceHHBUI::endFrame() {
        auto endTime = std::chrono::high_resolution_clock::now();
        auto frameTime = std::chrono::duration_cast<std::chrono::microseconds>(endTime - frameStartTime_);
        auto cpuTime = std::chrono::duration_cast<std::chrono::microseconds>(endTime - cpuStartTime_);
        
        stats_.frameTime.store(frameTime.count() / 1000.0f, std::memory_order_relaxed);
        stats_.cpuTime.store(cpuTime.count() / 1000.0f, std::memory_order_relaxed);
    }
    
    void RenderDeviceHHBUI::present() {
        if (swapChain_) {
            UINT syncInterval = config_.enableVSync ? 1 : 0;
            swapChain_->Present(syncInterval, 0);
        }
    }
    
    void RenderDeviceHHBUI::setViewport(const D3D11_VIEWPORT& viewport) {
        currentViewport_ = viewport;
        if (d3dContext_) {
            d3dContext_->RSSetViewports(1, &currentViewport_);
        }
    }
    
    void RenderDeviceHHBUI::setRenderTarget(ID3D11RenderTargetView* rtv, ID3D11DepthStencilView* dsv) {
        if (d3dContext_) {
            d3dContext_->OMSetRenderTargets(1, &rtv, dsv);
        }
    }
    
    void RenderDeviceHHBUI::clearRenderTarget(const float clearColor[4]) {
        if (d3dContext_ && backBufferRTV_) {
            d3dContext_->ClearRenderTargetView(backBufferRTV_.Get(), clearColor);
        }
    }
    
    void RenderDeviceHHBUI::clearDepthStencil(float depth, UINT8 stencil) {
        if (d3dContext_ && depthStencilView_) {
            d3dContext_->ClearDepthStencilView(depthStencilView_.Get(), 
                D3D11_CLEAR_DEPTH | D3D11_CLEAR_STENCIL, depth, stencil);
        }
    }
    
    bool RenderDeviceHHBUI::isFeatureSupported(D3D11_FEATURE feature) const {
        if (!d3dDevice_) {
            return false;
        }
        
        // 这里可以根据具体的feature类型进行检查
        // 简化实现，返回true
        return true;
    }
    
    DXGI_FORMAT RenderDeviceHHBUI::getSupportedFormat(const std::vector<DXGI_FORMAT>& candidates) const {
        if (!d3dDevice_) {
            return DXGI_FORMAT_UNKNOWN;
        }
        
        for (auto format : candidates) {
            UINT support = 0;
            if (SUCCEEDED(d3dDevice_->CheckFormatSupport(format, &support))) {
                if (support & D3D11_FORMAT_SUPPORT_RENDER_TARGET) {
                    return format;
                }
            }
        }
        
        return DXGI_FORMAT_UNKNOWN;
    }
    
    ResultHHBUI<ComPtr<ID3D11Buffer>> RenderDeviceHHBUI::createBuffer(
        const D3D11_BUFFER_DESC& desc, const D3D11_SUBRESOURCE_DATA* initialData) {
        
        if (!d3dDevice_) {
            return ErrorCodeHHBUI::NotInitialized;
        }
        
        ComPtr<ID3D11Buffer> buffer;
        HRESULT hr = d3dDevice_->CreateBuffer(&desc, initialData, &buffer);
        
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        return buffer;
    }
    
    ResultHHBUI<ComPtr<ID3D11Texture2D>> RenderDeviceHHBUI::createTexture2D(
        const D3D11_TEXTURE2D_DESC& desc, const D3D11_SUBRESOURCE_DATA* initialData) {
        
        if (!d3dDevice_) {
            return ErrorCodeHHBUI::NotInitialized;
        }
        
        ComPtr<ID3D11Texture2D> texture;
        HRESULT hr = d3dDevice_->CreateTexture2D(&desc, initialData, &texture);
        
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        return texture;
    }
    
    ResultHHBUI<ComPtr<ID3D11ShaderResourceView>> RenderDeviceHHBUI::createShaderResourceView(
        ID3D11Resource* resource, const D3D11_SHADER_RESOURCE_VIEW_DESC* desc) {
        
        if (!d3dDevice_) {
            return ErrorCodeHHBUI::NotInitialized;
        }
        
        ComPtr<ID3D11ShaderResourceView> srv;
        HRESULT hr = d3dDevice_->CreateShaderResourceView(resource, desc, &srv);
        
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        return srv;
    }
    
    ResultHHBUI<ComPtr<ID3D11RenderTargetView>> RenderDeviceHHBUI::createRenderTargetView(
        ID3D11Resource* resource, const D3D11_RENDER_TARGET_VIEW_DESC* desc) {
        
        if (!d3dDevice_) {
            return ErrorCodeHHBUI::NotInitialized;
        }
        
        ComPtr<ID3D11RenderTargetView> rtv;
        HRESULT hr = d3dDevice_->CreateRenderTargetView(resource, desc, &rtv);
        
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        return rtv;
    }
    
    // 私有辅助方法实现
    ResultHHBUI<void> RenderDeviceHHBUI::createD3DDevice() {
        UINT createDeviceFlags = 0;
        
        if (config_.enableDebugLayer) {
            createDeviceFlags |= D3D11_CREATE_DEVICE_DEBUG;
        }
        
        D3D_FEATURE_LEVEL featureLevels[] = {
            D3D_FEATURE_LEVEL_11_1,
            D3D_FEATURE_LEVEL_11_0,
            D3D_FEATURE_LEVEL_10_1,
            D3D_FEATURE_LEVEL_10_0
        };
        
        D3D_FEATURE_LEVEL featureLevel;
        
        HRESULT hr = D3D11CreateDevice(
            nullptr,                    // 使用默认适配器
            D3D_DRIVER_TYPE_HARDWARE,   // 硬件加速
            nullptr,                    // 软件光栅化器
            createDeviceFlags,          // 创建标志
            featureLevels,              // 特性级别数组
            ARRAYSIZE(featureLevels),   // 特性级别数量
            D3D11_SDK_VERSION,          // SDK版本
            &d3dDevice_,                // 输出设备
            &featureLevel,              // 输出特性级别
            &d3dContext_                // 输出设备上下文
        );
        
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        return {};
    }
    
    ResultHHBUI<void> RenderDeviceHHBUI::createSwapChain(HWND hwnd, UINT width, UINT height) {
        ComPtr<IDXGIFactory1> dxgiFactory;
        ComPtr<IDXGIDevice> dxgiDevice;
        ComPtr<IDXGIAdapter> dxgiAdapter;
        
        HRESULT hr = d3dDevice_->QueryInterface(__uuidof(IDXGIDevice), &dxgiDevice);
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        hr = dxgiDevice->GetAdapter(&dxgiAdapter);
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        hr = dxgiAdapter->GetParent(__uuidof(IDXGIFactory1), &dxgiFactory);
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        DXGI_SWAP_CHAIN_DESC swapChainDesc = {};
        swapChainDesc.BufferCount = 1;
        swapChainDesc.BufferDesc.Width = width;
        swapChainDesc.BufferDesc.Height = height;
        swapChainDesc.BufferDesc.Format = config_.backBufferFormat;
        swapChainDesc.BufferDesc.RefreshRate.Numerator = 60;
        swapChainDesc.BufferDesc.RefreshRate.Denominator = 1;
        swapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
        swapChainDesc.OutputWindow = hwnd;
        swapChainDesc.SampleDesc.Count = config_.enableMultisampling ? config_.multisampleCount : 1;
        swapChainDesc.SampleDesc.Quality = config_.enableMultisampling ? config_.multisampleQuality : 0;
        swapChainDesc.Windowed = TRUE;
        swapChainDesc.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;
        
        hr = dxgiFactory->CreateSwapChain(d3dDevice_.Get(), &swapChainDesc, &swapChain_);
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        return {};
    }
    
    ResultHHBUI<void> RenderDeviceHHBUI::createD2DResources() {
        HRESULT hr = D2D1CreateFactory(D2D1_FACTORY_TYPE_SINGLE_THREADED, &d2dFactory_);
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        hr = DWriteCreateFactory(DWRITE_FACTORY_TYPE_SHARED, __uuidof(IDWriteFactory), &dwriteFactory_);
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        hr = CoCreateInstance(CLSID_WICImagingFactory, nullptr, CLSCTX_INPROC_SERVER, 
            __uuidof(IWICImagingFactory), &wicFactory_);
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        return {};
    }
    
    ResultHHBUI<void> RenderDeviceHHBUI::createRenderTargets() {
        // 获取后缓冲区
        ComPtr<ID3D11Texture2D> backBuffer;
        HRESULT hr = swapChain_->GetBuffer(0, __uuidof(ID3D11Texture2D), &backBuffer);
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        // 创建渲染目标视图
        hr = d3dDevice_->CreateRenderTargetView(backBuffer.Get(), nullptr, &backBufferRTV_);
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        // 获取后缓冲区描述
        D3D11_TEXTURE2D_DESC backBufferDesc;
        backBuffer->GetDesc(&backBufferDesc);
        
        // 创建深度模板缓冲区
        D3D11_TEXTURE2D_DESC depthStencilDesc = {};
        depthStencilDesc.Width = backBufferDesc.Width;
        depthStencilDesc.Height = backBufferDesc.Height;
        depthStencilDesc.MipLevels = 1;
        depthStencilDesc.ArraySize = 1;
        depthStencilDesc.Format = config_.depthStencilFormat;
        depthStencilDesc.SampleDesc = backBufferDesc.SampleDesc;
        depthStencilDesc.Usage = D3D11_USAGE_DEFAULT;
        depthStencilDesc.BindFlags = D3D11_BIND_DEPTH_STENCIL;
        
        hr = d3dDevice_->CreateTexture2D(&depthStencilDesc, nullptr, &depthStencilBuffer_);
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        // 创建深度模板视图
        hr = d3dDevice_->CreateDepthStencilView(depthStencilBuffer_.Get(), nullptr, &depthStencilView_);
        if (FAILED(hr)) {
            return ErrorCodeHHBUI::Unknown;
        }
        
        // 设置渲染目标
        d3dContext_->OMSetRenderTargets(1, backBufferRTV_.GetAddressOf(), depthStencilView_.Get());
        
        return {};
    }
    
    // RenderContextHHBUI 实现
    void RenderContextHHBUI::drawPrimitive(D3D11_PRIMITIVE_TOPOLOGY topology, UINT vertexCount, UINT startVertex) {
        auto* context = device_->getD3DContext();
        if (context) {
            context->IASetPrimitiveTopology(topology);
            context->Draw(vertexCount, startVertex);
            device_->stats_.drawCalls.fetch_add(1, std::memory_order_relaxed);
        }
    }
    
    void RenderContextHHBUI::drawIndexed(D3D11_PRIMITIVE_TOPOLOGY topology, UINT indexCount, UINT startIndex, INT baseVertex) {
        auto* context = device_->getD3DContext();
        if (context) {
            context->IASetPrimitiveTopology(topology);
            context->DrawIndexed(indexCount, startIndex, baseVertex);
            device_->stats_.drawCalls.fetch_add(1, std::memory_order_relaxed);
        }
    }
    
    void RenderContextHHBUI::drawInstanced(D3D11_PRIMITIVE_TOPOLOGY topology, UINT vertexCountPerInstance, UINT instanceCount, UINT startVertex, UINT startInstance) {
        auto* context = device_->getD3DContext();
        if (context) {
            context->IASetPrimitiveTopology(topology);
            context->DrawInstanced(vertexCountPerInstance, instanceCount, startVertex, startInstance);
            device_->stats_.drawCalls.fetch_add(1, std::memory_order_relaxed);
        }
    }
    
    // RenderManagerHHBUI 实现
    ResultHHBUI<SharedPtr<RenderDeviceHHBUI>> RenderManagerHHBUI::createDevice(const RenderDeviceConfigHHBUI& config) {
        auto device = std::make_shared<RenderDeviceHHBUI>(config);
        
        HHBUI_SCOPED_LOCK(devicesMutex_);
        devices_.push_back(device);
        
        if (!defaultDevice_) {
            defaultDevice_ = device;
        }
        
        return device;
    }
    
    void RenderManagerHHBUI::destroyDevice(SharedPtr<RenderDeviceHHBUI> device) {
        if (!device) return;
        
        HHBUI_SCOPED_LOCK(devicesMutex_);
        
        auto it = std::find(devices_.begin(), devices_.end(), device);
        if (it != devices_.end()) {
            devices_.erase(it);
        }
        
        if (defaultDevice_ == device) {
            defaultDevice_ = devices_.empty() ? nullptr : devices_.front();
        }
    }
    
    void RenderManagerHHBUI::shutdown() {
        HHBUI_SCOPED_LOCK(devicesMutex_);
        
        for (auto& device : devices_) {
            device->shutdown();
        }
        
        devices_.clear();
        defaultDevice_.reset();
    }
    
} // namespace HHBUIPro::Engine
