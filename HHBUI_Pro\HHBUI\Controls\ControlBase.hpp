/**
** =====================================================================================
**
**       文件名称: ControlBase.hpp
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI Pro】现代化C++17控件基础架构 - 高性能UI控件系统基类
**
**       主要功能:
**       - 现代化C++17控件基础类与接口设计
**       - 高性能事件处理与消息路由系统
**       - 智能布局管理与自适应尺寸计算
**       - 现代化属性系统与数据绑定机制
**       - 线程安全的控件状态管理
**       - 高级动画与过渡效果支持
**       - 可扩展的控件渲染管线
**
**       技术特性:
**       - 采用现代C++17标准与智能指针技术
**       - RAII自动资源管理与异常安全保证
**       - 模板元编程与类型安全设计
**       - 高性能事件分发与处理机制
**       - 多线程安全的属性访问系统
**       - 现代化命名约定与API设计
**       - 可组合的控件架构模式
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化控件基础架构
**                             2. 实现高性能事件系统
**                             3. 添加智能布局管理
**                             4. 集成现代化属性系统
**                             5. 实现线程安全状态管理
**
** =====================================================================================
*/

#pragma once

#include "../Core/Base.hpp"
#include "../Core/Memory.hpp"
#include "../Core/Threading.hpp"
#include "../Engine/RenderEngine.hpp"
#include <string>
#include <string_view>
#include <unordered_map>
#include <vector>
#include <functional>
#include <optional>
#include <variant>
#include <any>
#include <chrono>

namespace HHBUIPro::Controls {
    
    // 前向声明
    class WindowHHBUI;
    class ControlHHBUI;
    class LayoutManagerHHBUI;
    
    // 现代化控件ID类型
    using ControlIdHHBUI = std::uint32_t;
    constexpr ControlIdHHBUI InvalidControlId = 0;
    
    // 控件状态枚举
    enum class ControlStateHHBUI : std::uint32_t {
        Normal = 0x00000001,
        Hover = 0x00000002,
        Pressed = 0x00000004,
        Focused = 0x00000008,
        Disabled = 0x00000010,
        Selected = 0x00000020,
        Checked = 0x00000040,
        Expanded = 0x00000080,
        Collapsed = 0x00000100,
        Visible = 0x00000200,
        Hidden = 0x00000400
    };
    
    // 位运算支持
    constexpr ControlStateHHBUI operator|(ControlStateHHBUI lhs, ControlStateHHBUI rhs) noexcept {
        return static_cast<ControlStateHHBUI>(static_cast<std::uint32_t>(lhs) | static_cast<std::uint32_t>(rhs));
    }
    
    constexpr ControlStateHHBUI operator&(ControlStateHHBUI lhs, ControlStateHHBUI rhs) noexcept {
        return static_cast<ControlStateHHBUI>(static_cast<std::uint32_t>(lhs) & static_cast<std::uint32_t>(rhs));
    }
    
    constexpr bool hasState(ControlStateHHBUI states, ControlStateHHBUI state) noexcept {
        return (states & state) == state;
    }
    
    // 控件事件类型
    enum class ControlEventTypeHHBUI : std::uint32_t {
        // 鼠标事件
        MouseEnter,
        MouseLeave,
        MouseMove,
        MouseDown,
        MouseUp,
        MouseClick,
        MouseDoubleClick,
        MouseWheel,
        
        // 键盘事件
        KeyDown,
        KeyUp,
        KeyPress,
        
        // 焦点事件
        FocusGained,
        FocusLost,
        
        // 控件生命周期事件
        Created,
        Destroyed,
        Shown,
        Hidden,
        
        // 布局事件
        SizeChanged,
        PositionChanged,
        LayoutUpdated,
        
        // 状态变化事件
        StateChanged,
        PropertyChanged,
        
        // 自定义事件
        Custom = 0x10000000
    };
    
    // 事件参数基类
    struct ControlEventArgsHHBUI {
        ControlEventTypeHHBUI eventType;
        ControlHHBUI* source = nullptr;
        std::chrono::steady_clock::time_point timestamp = std::chrono::steady_clock::now();
        bool handled = false;
        
        explicit ControlEventArgsHHBUI(ControlEventTypeHHBUI type) : eventType(type) {}
        virtual ~ControlEventArgsHHBUI() = default;
    };
    
    // 鼠标事件参数
    struct MouseEventArgsHHBUI : public ControlEventArgsHHBUI {
        int x = 0, y = 0;
        int deltaX = 0, deltaY = 0;
        int wheelDelta = 0;
        bool leftButton = false;
        bool rightButton = false;
        bool middleButton = false;
        bool ctrlKey = false;
        bool shiftKey = false;
        bool altKey = false;
        
        explicit MouseEventArgsHHBUI(ControlEventTypeHHBUI type) : ControlEventArgsHHBUI(type) {}
    };
    
    // 键盘事件参数
    struct KeyboardEventArgsHHBUI : public ControlEventArgsHHBUI {
        int keyCode = 0;
        wchar_t character = 0;
        bool ctrlKey = false;
        bool shiftKey = false;
        bool altKey = false;
        bool repeat = false;
        
        explicit KeyboardEventArgsHHBUI(ControlEventTypeHHBUI type) : ControlEventArgsHHBUI(type) {}
    };
    
    // 尺寸和位置类型
    struct SizeHHBUI {
        float width = 0.0f;
        float height = 0.0f;
        
        SizeHHBUI() = default;
        SizeHHBUI(float w, float h) : width(w), height(h) {}
        
        bool operator==(const SizeHHBUI& other) const noexcept {
            return std::abs(width - other.width) < 1e-6f && std::abs(height - other.height) < 1e-6f;
        }
        
        bool operator!=(const SizeHHBUI& other) const noexcept {
            return !(*this == other);
        }
    };
    
    struct PointHHBUI {
        float x = 0.0f;
        float y = 0.0f;
        
        PointHHBUI() = default;
        PointHHBUI(float x_, float y_) : x(x_), y(y_) {}
        
        bool operator==(const PointHHBUI& other) const noexcept {
            return std::abs(x - other.x) < 1e-6f && std::abs(y - other.y) < 1e-6f;
        }
        
        bool operator!=(const PointHHBUI& other) const noexcept {
            return !(*this == other);
        }
    };
    
    struct RectHHBUI {
        float x = 0.0f;
        float y = 0.0f;
        float width = 0.0f;
        float height = 0.0f;
        
        RectHHBUI() = default;
        RectHHBUI(float x_, float y_, float w, float h) : x(x_), y(y_), width(w), height(h) {}
        RectHHBUI(const PointHHBUI& pos, const SizeHHBUI& size) : x(pos.x), y(pos.y), width(size.width), height(size.height) {}
        
        PointHHBUI getPosition() const noexcept { return {x, y}; }
        SizeHHBUI getSize() const noexcept { return {width, height}; }
        
        float getLeft() const noexcept { return x; }
        float getTop() const noexcept { return y; }
        float getRight() const noexcept { return x + width; }
        float getBottom() const noexcept { return y + height; }
        
        bool contains(const PointHHBUI& point) const noexcept {
            return point.x >= x && point.x < x + width && point.y >= y && point.y < y + height;
        }
        
        bool intersects(const RectHHBUI& other) const noexcept {
            return !(getRight() <= other.getLeft() || other.getRight() <= getLeft() ||
                     getBottom() <= other.getTop() || other.getBottom() <= getTop());
        }
        
        RectHHBUI intersect(const RectHHBUI& other) const noexcept {
            float left = std::max(getLeft(), other.getLeft());
            float top = std::max(getTop(), other.getTop());
            float right = std::min(getRight(), other.getRight());
            float bottom = std::min(getBottom(), other.getBottom());
            
            if (left < right && top < bottom) {
                return {left, top, right - left, bottom - top};
            }
            return {};
        }
        
        bool operator==(const RectHHBUI& other) const noexcept {
            return std::abs(x - other.x) < 1e-6f && std::abs(y - other.y) < 1e-6f &&
                   std::abs(width - other.width) < 1e-6f && std::abs(height - other.height) < 1e-6f;
        }
        
        bool operator!=(const RectHHBUI& other) const noexcept {
            return !(*this == other);
        }
    };
    
    // 颜色类型
    struct ColorHHBUI {
        float r = 0.0f;
        float g = 0.0f;
        float b = 0.0f;
        float a = 1.0f;
        
        ColorHHBUI() = default;
        ColorHHBUI(float red, float green, float blue, float alpha = 1.0f) 
            : r(red), g(green), b(blue), a(alpha) {}
        
        explicit ColorHHBUI(std::uint32_t argb) {
            a = ((argb >> 24) & 0xFF) / 255.0f;
            r = ((argb >> 16) & 0xFF) / 255.0f;
            g = ((argb >> 8) & 0xFF) / 255.0f;
            b = (argb & 0xFF) / 255.0f;
        }
        
        std::uint32_t toARGB() const noexcept {
            return (static_cast<std::uint32_t>(a * 255) << 24) |
                   (static_cast<std::uint32_t>(r * 255) << 16) |
                   (static_cast<std::uint32_t>(g * 255) << 8) |
                   static_cast<std::uint32_t>(b * 255);
        }
        
        // 预定义颜色
        static const ColorHHBUI Transparent;
        static const ColorHHBUI Black;
        static const ColorHHBUI White;
        static const ColorHHBUI Red;
        static const ColorHHBUI Green;
        static const ColorHHBUI Blue;
        static const ColorHHBUI Yellow;
        static const ColorHHBUI Cyan;
        static const ColorHHBUI Magenta;
    };
    
    // 事件处理器类型
    using ControlEventHandlerHHBUI = std::function<void(ControlEventArgsHHBUI&)>;
    
    // 属性变化通知
    struct PropertyChangedEventArgsHHBUI : public ControlEventArgsHHBUI {
        std::string propertyName;
        std::any oldValue;
        std::any newValue;
        
        PropertyChangedEventArgsHHBUI(std::string name, std::any old, std::any new_)
            : ControlEventArgsHHBUI(ControlEventTypeHHBUI::PropertyChanged)
            , propertyName(std::move(name))
            , oldValue(std::move(old))
            , newValue(std::move(new_)) {}
    };
    
    // 控件基类接口
    class IControlHHBUI {
    public:
        virtual ~IControlHHBUI() = default;
        
        // 基本属性
        virtual ControlIdHHBUI getId() const noexcept = 0;
        virtual std::string_view getName() const noexcept = 0;
        virtual void setName(std::string_view name) = 0;
        
        // 几何属性
        virtual RectHHBUI getBounds() const noexcept = 0;
        virtual void setBounds(const RectHHBUI& bounds) = 0;
        virtual PointHHBUI getPosition() const noexcept = 0;
        virtual void setPosition(const PointHHBUI& position) = 0;
        virtual SizeHHBUI getSize() const noexcept = 0;
        virtual void setSize(const SizeHHBUI& size) = 0;
        
        // 状态管理
        virtual ControlStateHHBUI getState() const noexcept = 0;
        virtual void setState(ControlStateHHBUI state) = 0;
        virtual bool hasState(ControlStateHHBUI state) const noexcept = 0;
        virtual void addState(ControlStateHHBUI state) = 0;
        virtual void removeState(ControlStateHHBUI state) = 0;
        
        // 可见性
        virtual bool isVisible() const noexcept = 0;
        virtual void setVisible(bool visible) = 0;
        virtual bool isEnabled() const noexcept = 0;
        virtual void setEnabled(bool enabled) = 0;
        
        // 层次结构
        virtual IControlHHBUI* getParent() const noexcept = 0;
        virtual void setParent(IControlHHBUI* parent) = 0;
        virtual std::vector<IControlHHBUI*> getChildren() const = 0;
        virtual void addChild(UniquePtr<IControlHHBUI> child) = 0;
        virtual UniquePtr<IControlHHBUI> removeChild(IControlHHBUI* child) = 0;
        
        // 事件处理
        virtual void addEventListener(ControlEventTypeHHBUI eventType, ControlEventHandlerHHBUI handler) = 0;
        virtual void removeEventListener(ControlEventTypeHHBUI eventType) = 0;
        virtual void fireEvent(UniquePtr<ControlEventArgsHHBUI> args) = 0;
        
        // 渲染
        virtual void render(Engine::RenderContextHHBUI& context, const RectHHBUI& clipRect) = 0;
        virtual void invalidate(const RectHHBUI& rect = {}) = 0;
        
        // 布局
        virtual SizeHHBUI measureDesiredSize(const SizeHHBUI& availableSize) = 0;
        virtual void arrange(const RectHHBUI& finalRect) = 0;
        
        // 命中测试
        virtual IControlHHBUI* hitTest(const PointHHBUI& point) = 0;
        
        // 属性系统
        virtual void setProperty(std::string_view name, std::any value) = 0;
        virtual std::optional<std::any> getProperty(std::string_view name) const = 0;
    };
    
} // namespace HHBUIPro::Controls
