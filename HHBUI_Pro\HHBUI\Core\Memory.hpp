/**
** =====================================================================================
**
**       文件名称: Memory.hpp
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI Pro】现代化C++17内存管理系统 - 高性能内存池与智能分配器
**
**       主要功能:
**       - 现代化C++17内存管理与智能分配器
**       - 高性能对象池与内存池化技术
**       - 线程安全的内存分配与回收机制
**       - RAII自动资源管理与异常安全保证
**       - 内存泄漏检测与调试诊断工具
**       - 自定义分配器与STL容器集成
**       - 内存对齐与缓存友好的数据结构
**
**       技术特性:
**       - 采用现代C++17标准与智能指针技术
**       - 无锁内存池与原子操作优化
**       - 模板元编程与编译期内存布局优化
**       - 异常安全保证与强异常安全级别
**       - 内存使用统计与性能监控
**       - 自适应内存池大小调整算法
**       - 内存碎片整理与压缩机制
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化内存管理系统
**                             2. 实现高性能对象池技术
**                             3. 添加线程安全内存分配器
**                             4. 集成RAII与异常安全机制
**                             5. 实现内存泄漏检测工具
**
** =====================================================================================
*/

#pragma once

#include "Base.hpp"
#include <memory_resource>
#include <new>
#include <cstdlib>
#include <cstring>
#include <algorithm>
#include <vector>
#include <unordered_map>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <cassert>

namespace HHBUIPro {
    
    // 内存对齐工具
    template<std::size_t Alignment>
    constexpr std::size_t alignSize(std::size_t size) noexcept {
        return (size + Alignment - 1) & ~(Alignment - 1);
    }
    
    constexpr std::size_t getDefaultAlignment() noexcept {
        return alignof(std::max_align_t);
    }
    
    // 内存统计信息
    struct MemoryStatsHHBUI {
        std::atomic<std::size_t> totalAllocated{0};
        std::atomic<std::size_t> totalDeallocated{0};
        std::atomic<std::size_t> currentUsage{0};
        std::atomic<std::size_t> peakUsage{0};
        std::atomic<std::size_t> allocationCount{0};
        std::atomic<std::size_t> deallocationCount{0};
        
        void recordAllocation(std::size_t size) noexcept {
            totalAllocated.fetch_add(size, std::memory_order_relaxed);
            allocationCount.fetch_add(1, std::memory_order_relaxed);
            
            auto current = currentUsage.fetch_add(size, std::memory_order_relaxed) + size;
            auto peak = peakUsage.load(std::memory_order_relaxed);
            while (current > peak && !peakUsage.compare_exchange_weak(peak, current, std::memory_order_relaxed)) {
                // 自旋直到成功更新峰值
            }
        }
        
        void recordDeallocation(std::size_t size) noexcept {
            totalDeallocated.fetch_add(size, std::memory_order_relaxed);
            deallocationCount.fetch_add(1, std::memory_order_relaxed);
            currentUsage.fetch_sub(size, std::memory_order_relaxed);
        }
        
        std::size_t getCurrentUsage() const noexcept {
            return currentUsage.load(std::memory_order_relaxed);
        }
        
        std::size_t getPeakUsage() const noexcept {
            return peakUsage.load(std::memory_order_relaxed);
        }
        
        double getFragmentationRatio() const noexcept {
            auto allocated = totalAllocated.load(std::memory_order_relaxed);
            auto deallocated = totalDeallocated.load(std::memory_order_relaxed);
            if (allocated == 0) return 0.0;
            return static_cast<double>(deallocated) / allocated;
        }
    };
    
    // 现代化内存分配器基类
    class MemoryAllocatorHHBUI {
    public:
        virtual ~MemoryAllocatorHHBUI() = default;
        
        virtual void* allocate(std::size_t size, std::size_t alignment = getDefaultAlignment()) = 0;
        virtual void deallocate(void* ptr, std::size_t size) noexcept = 0;
        virtual bool owns(void* ptr) const noexcept = 0;
        
        virtual MemoryStatsHHBUI getStats() const noexcept = 0;
        virtual void reset() noexcept = 0;
        
        // 便利方法
        template<typename T, typename... Args>
        T* construct(Args&&... args) {
            void* ptr = allocate(sizeof(T), alignof(T));
            try {
                return new(ptr) T(std::forward<Args>(args)...);
            } catch (...) {
                deallocate(ptr, sizeof(T));
                throw;
            }
        }
        
        template<typename T>
        void destroy(T* ptr) noexcept {
            if (ptr) {
                ptr->~T();
                deallocate(ptr, sizeof(T));
            }
        }
    };
    
    // 高性能对象池
    template<typename T, std::size_t ChunkSize = 1024>
    class ObjectPoolHHBUI {
    private:
        struct alignas(T) Block {
            std::byte data[sizeof(T)];
            Block* next;
        };
        
        struct Chunk {
            alignas(Block) std::byte storage[ChunkSize * sizeof(Block)];
            std::unique_ptr<Chunk> next;
            
            Block* getBlock(std::size_t index) noexcept {
                return reinterpret_cast<Block*>(storage + index * sizeof(Block));
            }
        };
        
    public:
        ObjectPoolHHBUI() {
            allocateChunk();
        }
        
        ~ObjectPoolHHBUI() {
            clear();
        }
        
        // 禁用拷贝，允许移动
        ObjectPoolHHBUI(const ObjectPoolHHBUI&) = delete;
        ObjectPoolHHBUI& operator=(const ObjectPoolHHBUI&) = delete;
        ObjectPoolHHBUI(ObjectPoolHHBUI&&) = default;
        ObjectPoolHHBUI& operator=(ObjectPoolHHBUI&&) = default;
        
        template<typename... Args>
        T* acquire(Args&&... args) {
            std::unique_lock lock(mutex_);
            
            if (!freeList_) {
                allocateChunk();
            }
            
            Block* block = freeList_;
            freeList_ = freeList_->next;
            
            lock.unlock();
            
            try {
                return new(block) T(std::forward<Args>(args)...);
            } catch (...) {
                // 异常安全：将块返回到空闲列表
                lock.lock();
                block->next = freeList_;
                freeList_ = block;
                throw;
            }
        }
        
        void release(T* obj) noexcept {
            if (!obj) return;
            
            obj->~T();
            
            Block* block = reinterpret_cast<Block*>(obj);
            
            std::unique_lock lock(mutex_);
            block->next = freeList_;
            freeList_ = block;
        }
        
        void clear() noexcept {
            std::unique_lock lock(mutex_);
            chunks_.reset();
            freeList_ = nullptr;
        }
        
        std::size_t getChunkCount() const noexcept {
            std::shared_lock lock(mutex_);
            std::size_t count = 0;
            for (auto* chunk = chunks_.get(); chunk; chunk = chunk->next.get()) {
                ++count;
            }
            return count;
        }
        
    private:
        void allocateChunk() {
            auto newChunk = std::make_unique<Chunk>();
            
            // 初始化空闲列表
            for (std::size_t i = 0; i < ChunkSize - 1; ++i) {
                newChunk->getBlock(i)->next = newChunk->getBlock(i + 1);
            }
            newChunk->getBlock(ChunkSize - 1)->next = freeList_;
            freeList_ = newChunk->getBlock(0);
            
            // 链接到块列表
            newChunk->next = std::move(chunks_);
            chunks_ = std::move(newChunk);
        }
        
        mutable std::shared_mutex mutex_;
        std::unique_ptr<Chunk> chunks_;
        Block* freeList_ = nullptr;
    };
    
    // 线程安全的内存池分配器
    class PoolAllocatorHHBUI : public MemoryAllocatorHHBUI {
    private:
        struct Pool {
            std::size_t blockSize;
            std::size_t blockCount;
            std::unique_ptr<std::byte[]> memory;
            std::vector<void*> freeBlocks;
            std::mutex mutex;
            
            Pool(std::size_t size, std::size_t count)
                : blockSize(alignSize<getDefaultAlignment()>(size))
                , blockCount(count)
                , memory(std::make_unique<std::byte[]>(blockSize * count)) {
                
                freeBlocks.reserve(count);
                for (std::size_t i = 0; i < count; ++i) {
                    freeBlocks.push_back(memory.get() + i * blockSize);
                }
            }
            
            void* allocate() {
                std::unique_lock lock(mutex);
                if (freeBlocks.empty()) {
                    return nullptr;
                }
                
                void* ptr = freeBlocks.back();
                freeBlocks.pop_back();
                return ptr;
            }
            
            bool deallocate(void* ptr) {
                if (!owns(ptr)) return false;
                
                std::unique_lock lock(mutex);
                freeBlocks.push_back(ptr);
                return true;
            }
            
            bool owns(void* ptr) const noexcept {
                auto* bytePtr = static_cast<std::byte*>(ptr);
                return bytePtr >= memory.get() && 
                       bytePtr < memory.get() + blockSize * blockCount;
            }
            
            std::size_t getUsedBlocks() const {
                std::unique_lock lock(mutex);
                return blockCount - freeBlocks.size();
            }
        };
        
    public:
        explicit PoolAllocatorHHBUI(std::vector<std::pair<std::size_t, std::size_t>> poolConfigs) {
            pools_.reserve(poolConfigs.size());
            for (const auto& [size, count] : poolConfigs) {
                pools_.emplace_back(std::make_unique<Pool>(size, count));
            }
            
            // 按块大小排序以便快速查找
            std::sort(pools_.begin(), pools_.end(),
                [](const auto& a, const auto& b) {
                    return a->blockSize < b->blockSize;
                });
        }
        
        void* allocate(std::size_t size, std::size_t alignment = getDefaultAlignment()) override {
            size = alignSize<getDefaultAlignment()>(size);
            
            // 查找合适的池
            for (auto& pool : pools_) {
                if (pool->blockSize >= size) {
                    if (void* ptr = pool->allocate()) {
                        stats_.recordAllocation(pool->blockSize);
                        return ptr;
                    }
                }
            }
            
            // 池中没有可用块，使用系统分配器
            void* ptr = std::aligned_alloc(alignment, size);
            if (!ptr) {
                throw std::bad_alloc();
            }
            
            {
                std::unique_lock lock(systemAllocsMutex_);
                systemAllocs_[ptr] = size;
            }
            
            stats_.recordAllocation(size);
            return ptr;
        }
        
        void deallocate(void* ptr, std::size_t size) noexcept override {
            if (!ptr) return;
            
            // 尝试从池中释放
            for (auto& pool : pools_) {
                if (pool->deallocate(ptr)) {
                    stats_.recordDeallocation(pool->blockSize);
                    return;
                }
            }
            
            // 从系统分配器释放
            {
                std::unique_lock lock(systemAllocsMutex_);
                auto it = systemAllocs_.find(ptr);
                if (it != systemAllocs_.end()) {
                    stats_.recordDeallocation(it->second);
                    systemAllocs_.erase(it);
                }
            }
            
            std::free(ptr);
        }
        
        bool owns(void* ptr) const noexcept override {
            for (const auto& pool : pools_) {
                if (pool->owns(ptr)) {
                    return true;
                }
            }
            
            std::shared_lock lock(systemAllocsMutex_);
            return systemAllocs_.find(ptr) != systemAllocs_.end();
        }
        
        MemoryStatsHHBUI getStats() const noexcept override {
            return stats_;
        }
        
        void reset() noexcept override {
            // 注意：这会使所有已分配的指针无效
            for (auto& pool : pools_) {
                std::unique_lock lock(pool->mutex);
                pool->freeBlocks.clear();
                for (std::size_t i = 0; i < pool->blockCount; ++i) {
                    pool->freeBlocks.push_back(pool->memory.get() + i * pool->blockSize);
                }
            }
            
            {
                std::unique_lock lock(systemAllocsMutex_);
                for (const auto& [ptr, size] : systemAllocs_) {
                    std::free(ptr);
                }
                systemAllocs_.clear();
            }
            
            stats_ = {};
        }
        
    private:
        std::vector<std::unique_ptr<Pool>> pools_;
        mutable std::shared_mutex systemAllocsMutex_;
        std::unordered_map<void*, std::size_t> systemAllocs_;
        mutable MemoryStatsHHBUI stats_;
    };
    
    // 全局内存管理器
    class MemoryManagerHHBUI {
    public:
        static MemoryManagerHHBUI& getInstance() {
            static MemoryManagerHHBUI instance;
            return instance;
        }
        
        void setDefaultAllocator(std::unique_ptr<MemoryAllocatorHHBUI> allocator) {
            std::unique_lock lock(mutex_);
            defaultAllocator_ = std::move(allocator);
        }
        
        MemoryAllocatorHHBUI* getDefaultAllocator() const {
            std::shared_lock lock(mutex_);
            return defaultAllocator_.get();
        }
        
        MemoryStatsHHBUI getGlobalStats() const {
            std::shared_lock lock(mutex_);
            return defaultAllocator_ ? defaultAllocator_->getStats() : MemoryStatsHHBUI{};
        }
        
    private:
        MemoryManagerHHBUI() {
            // 默认配置：常见大小的内存池
            std::vector<std::pair<std::size_t, std::size_t>> defaultPools = {
                {16, 1024},    // 小对象
                {32, 512},     // 小对象
                {64, 256},     // 中等对象
                {128, 128},    // 中等对象
                {256, 64},     // 大对象
                {512, 32},     // 大对象
                {1024, 16},    // 很大对象
                {2048, 8}      // 非常大对象
            };
            
            defaultAllocator_ = std::make_unique<PoolAllocatorHHBUI>(std::move(defaultPools));
        }
        
        mutable std::shared_mutex mutex_;
        std::unique_ptr<MemoryAllocatorHHBUI> defaultAllocator_;
    };
    
} // namespace HHBUIPro
